#!/usr/bin/env python3
"""
Test script to verify the Unicode decode error fix
"""

import sys
import os
import json

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.services.blockchain_service import BlockchainService

def test_receipt_json_serialization():
    """Test that blockchain receipts can be properly JSON serialized"""
    
    print("🧪 Testing Receipt JSON Serialization")
    print("=" * 60)
    
    # Create a mock receipt with binary data (similar to what Web3 returns)
    class MockReceipt:
        def __init__(self):
            self.status = 1
            self.blockNumber = 12345
            self.gasUsed = 200000
            self.transactionHash = b'\x7d\xd5\x11\n\x04\xf2H\x1a\xfb\xd4k\xc3\xac\x02\x32\xd3,\xbe\x19\x94\xa2\x95\xc3N\xd2\x05\xad\x56_\xb2\xb6s'  # Binary hash
            self.logs = []
            self.contractAddress = None
            self.cumulativeGasUsed = 200000
            self.effectiveGasPrice = 1000000000
            self.from_address = b'\x1f\x67\xf0\xa4\xb2A\x6b\xb3\xaeB\xa0k\xdf\xdc\"\x78H\x99\x87\x16'  # Binary address
            self.to = b'(\xb3\x17YKD\x83\xd2N\xe8\xad\xcb\x13\xa1\xb1H\x84\x97\xc6\xba'  # Binary address
    
    # Test the blockchain service's receipt conversion
    blockchain_service = BlockchainService()
    
    try:
        # Test with mock receipt
        mock_receipt = MockReceipt()
        print(f"📝 Testing with mock receipt containing binary data...")
        
        # Convert to JSON-safe format
        json_safe_receipt = blockchain_service._make_receipt_json_safe(mock_receipt)
        
        print(f"✅ Converted receipt to JSON-safe format")
        print(f"   Status: {json_safe_receipt.get('status')}")
        print(f"   Block Number: {json_safe_receipt.get('blockNumber')}")
        print(f"   Gas Used: {json_safe_receipt.get('gasUsed')}")
        print(f"   Transaction Hash: {json_safe_receipt.get('transactionHash')}")
        
        # Test JSON serialization
        try:
            json_string = json.dumps(json_safe_receipt)
            print(f"✅ Successfully serialized to JSON ({len(json_string)} characters)")
            
            # Test deserialization
            deserialized = json.loads(json_string)
            print(f"✅ Successfully deserialized from JSON")
            
            return True
            
        except Exception as json_error:
            print(f"❌ JSON serialization failed: {str(json_error)}")
            return False
            
    except Exception as e:
        print(f"❌ Receipt conversion failed: {str(e)}")
        return False

def test_transaction_hash_normalization():
    """Test transaction hash normalization with various formats"""
    
    print("\n🔧 Testing Transaction Hash Normalization")
    print("=" * 60)
    
    test_cases = [
        {
            "input": b'\x7d\xd5\x11\n\x04\xf2H\x1a\xfb\xd4k\xc3\xac\x02\x32\xd3,\xbe\x19\x94\xa2\x95\xc3N\xd2\x05\xad\x56_\xb2\xb6s',
            "expected": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "description": "Binary hash (bytes)"
        },
        {
            "input": "7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "expected": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "description": "Hex string without 0x prefix"
        },
        {
            "input": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "expected": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "description": "Hex string with 0x prefix"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['description']}")
        
        try:
            if isinstance(test_case['input'], bytes):
                # Convert bytes to hex string first
                hex_string = test_case['input'].hex()
                normalized = BlockchainService.normalize_tx_hash(hex_string)
            else:
                normalized = BlockchainService.normalize_tx_hash(test_case['input'])
            
            print(f"   Input: {test_case['input']}")
            print(f"   Output: {normalized}")
            print(f"   Expected: {test_case['expected']}")
            
            if normalized == test_case['expected']:
                print(f"   ✅ Test passed")
            else:
                print(f"   ❌ Test failed")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ Test error: {str(e)}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Starting Unicode Decode Error Fix Tests")
    print("=" * 60)
    
    # Run tests
    receipt_passed = test_receipt_json_serialization()
    hash_passed = test_transaction_hash_normalization()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if receipt_passed and hash_passed:
        print("🎉 ALL TESTS PASSED! The Unicode decode error fix is working correctly.")
        print("\n✅ Key improvements:")
        print("   • Blockchain receipts are properly converted to JSON-safe format")
        print("   • Binary data is converted to hex strings with 0x prefix")
        print("   • Transaction hashes are properly normalized")
        print("   • No more Unicode decode errors in FastAPI responses")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED! Please review the implementation.")
        if not receipt_passed:
            print("   • Receipt JSON serialization failed")
        if not hash_passed:
            print("   • Transaction hash normalization failed")
        sys.exit(1)
