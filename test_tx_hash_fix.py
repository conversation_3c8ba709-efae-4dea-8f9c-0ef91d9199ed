#!/usr/bin/env python3
"""
Test script to verify the transaction hash formatting fix
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.services.blockchain_service import BlockchainService

def test_transaction_hash_validation():
    """Test the transaction hash validation and normalization functions"""
    
    print("🧪 Testing Transaction Hash Validation and Normalization")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        # Valid hashes
        {
            "hash": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "expected_valid": True,
            "expected_normalized": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "description": "Valid hash with 0x prefix"
        },
        {
            "hash": "7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "expected_valid": True,
            "expected_normalized": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
            "description": "Valid hash without 0x prefix"
        },
        # Invalid hashes
        {
            "hash": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b67",  # 63 chars
            "expected_valid": False,
            "expected_normalized": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b67",
            "description": "Hash too short (63 chars)"
        },
        {
            "hash": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b6733",  # 67 chars
            "expected_valid": False,
            "expected_normalized": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b6733",
            "description": "Hash too long (67 chars)"
        },
        {
            "hash": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b67g",  # Invalid hex
            "expected_valid": False,
            "expected_normalized": "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b67g",
            "description": "Invalid hex character"
        },
        {
            "hash": "",
            "expected_valid": False,
            "expected_normalized": "",
            "description": "Empty string"
        },
        {
            "hash": None,
            "expected_valid": False,
            "expected_normalized": "",
            "description": "None value"
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['description']}")
        print(f"   Input: {test_case['hash']}")
        
        # Test validation
        try:
            is_valid = BlockchainService.is_valid_tx_hash(test_case['hash'])
            print(f"   Valid: {is_valid} (expected: {test_case['expected_valid']})")
            
            if is_valid != test_case['expected_valid']:
                print(f"   ❌ VALIDATION FAILED!")
                all_passed = False
            else:
                print(f"   ✅ Validation passed")
        except Exception as e:
            print(f"   ❌ VALIDATION ERROR: {str(e)}")
            all_passed = False
        
        # Test normalization
        try:
            normalized = BlockchainService.normalize_tx_hash(test_case['hash'] or "")
            print(f"   Normalized: {normalized}")
            print(f"   Expected:   {test_case['expected_normalized']}")
            
            if normalized != test_case['expected_normalized']:
                print(f"   ❌ NORMALIZATION FAILED!")
                all_passed = False
            else:
                print(f"   ✅ Normalization passed")
        except Exception as e:
            print(f"   ❌ NORMALIZATION ERROR: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Transaction hash fix is working correctly.")
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
    
    return all_passed

def test_basescan_url_generation():
    """Test Basescan URL generation with proper 0x prefix"""
    
    print("\n🔗 Testing Basescan URL Generation")
    print("=" * 60)
    
    test_hashes = [
        "0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673",
        "7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673"
    ]
    
    expected_url = "https://sepolia.basescan.org/tx/0x7dd5110a04f2481afbd46bc3ac0232d32cbe1994a295c34ed205ad565fb2b673"
    
    all_passed = True
    
    for i, tx_hash in enumerate(test_hashes, 1):
        print(f"\n🔍 Test {i}: Hash = {tx_hash}")
        
        # Normalize the hash
        normalized_hash = BlockchainService.normalize_tx_hash(tx_hash)
        generated_url = f"https://sepolia.basescan.org/tx/{normalized_hash}"
        
        print(f"   Generated URL: {generated_url}")
        print(f"   Expected URL:  {expected_url}")
        
        if generated_url == expected_url:
            print(f"   ✅ URL generation passed")
        else:
            print(f"   ❌ URL generation failed!")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 URL GENERATION TESTS PASSED!")
    else:
        print("❌ URL GENERATION TESTS FAILED!")
    
    return all_passed

if __name__ == "__main__":
    print("🚀 Starting Transaction Hash Fix Tests")
    print("=" * 60)
    
    # Run tests
    validation_passed = test_transaction_hash_validation()
    url_passed = test_basescan_url_generation()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if validation_passed and url_passed:
        print("🎉 ALL TESTS PASSED! The transaction hash formatting fix is working correctly.")
        print("\n✅ Key improvements:")
        print("   • Transaction hashes are properly validated")
        print("   • 0x prefix is correctly handled and normalized")
        print("   • Basescan URLs are properly formatted")
        print("   • Both with and without 0x prefix inputs are supported")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED! Please review the implementation.")
        sys.exit(1)
