"""
Medical records endpoints
"""
import hashlib
import time
from fastapi import APIRout<PERSON>, Body, Request, Depends
from typing import Dict, Any

from backend.models.records import RecordSign, RecordStore, RecordRetrieve, RecordResponse
from backend.config import success_response, error_response, DOCTOR_ADDRESS, PATIENT_ADDRESS
from backend.data import MerkleService, encrypt_record, decrypt_record
from backend.groupsig_utils import sign_message, verify_signature
from backend.services.key_manager import KeyManager
from backend.services.ipfs_service import IPFSService
from backend.services.blockchain_service import get_blockchain_service

router = APIRouter()

def safe_success_response(data=None, message="Success"):
    """
    Wrapper for success_response that ensures all data is sanitized
    """
    try:
        sanitized_data = _sanitize_for_json(data) if data is not None else None
        return success_response(data=sanitized_data, message=message)
    except Exception as e:
        print(f"❌ Error in safe_success_response: {str(e)}")
        print(f"   Data type: {type(data).__name__}")
        print(f"   Data preview: {str(data)[:200] if data else 'None'}")
        # Return a minimal safe response
        return success_response(data={"error": f"Response sanitization failed: {str(e)}"}, message="Partial Success")

def safe_error_response(message, status_code=500):
    """
    Wrapper for error_response that ensures the message is safe
    """
    try:
        safe_message = _sanitize_for_json(message)
        if isinstance(safe_message, str):
            return error_response(safe_message, status_code)
        else:
            # If sanitization returns non-string, convert to string
            return error_response(str(safe_message), status_code)
    except Exception as e:
        print(f"❌ Error in safe_error_response: {str(e)}")
        # Return a simple error without trying to sanitize again
        return error_response("Error response sanitization failed", status_code)

def _sanitize_for_json(obj):
    """
    Comprehensive sanitization to ensure data is JSON-serializable by converting binary data to safe formats
    """
    import base64
    import logging

    # Set up debug logging
    logger = logging.getLogger(__name__)

    try:
        if obj is None:
            return None
        elif isinstance(obj, bytes):
            # Convert bytes to base64 string
            logger.debug(f"Converting bytes to base64: {len(obj)} bytes")
            return base64.b64encode(obj).decode('utf-8')
        elif isinstance(obj, dict):
            # Recursively sanitize dictionary values
            sanitized = {}
            for k, v in obj.items():
                try:
                    sanitized[str(k)] = _sanitize_for_json(v)
                except Exception as e:
                    logger.warning(f"Error sanitizing dict key '{k}': {e}")
                    sanitized[str(k)] = f"<sanitization_error: {type(v).__name__}>"
            return sanitized
        elif isinstance(obj, (list, tuple)):
            # Recursively sanitize list/tuple items
            sanitized = []
            for i, item in enumerate(obj):
                try:
                    sanitized.append(_sanitize_for_json(item))
                except Exception as e:
                    logger.warning(f"Error sanitizing list item {i}: {e}")
                    sanitized.append(f"<sanitization_error: {type(item).__name__}>")
            return sanitized
        elif isinstance(obj, str):
            # Ensure string is valid UTF-8
            try:
                # Try to encode/decode to verify it's valid UTF-8
                obj.encode('utf-8').decode('utf-8')
                return obj
            except (UnicodeEncodeError, UnicodeDecodeError):
                # If string contains invalid UTF-8, encode as base64
                logger.warning(f"Invalid UTF-8 string detected, converting to base64")
                return base64.b64encode(obj.encode('utf-8', errors='replace')).decode('utf-8')
        elif hasattr(obj, 'hex'):
            # Handle HexBytes and similar objects
            try:
                hex_str = obj.hex()
                return f"0x{hex_str}" if not hex_str.startswith('0x') else hex_str
            except Exception as e:
                logger.warning(f"Error converting hex object: {e}")
                return f"<hex_error: {type(obj).__name__}>"
        elif hasattr(obj, '__dict__'):
            # Handle custom objects by converting their attributes
            try:
                obj_dict = {}
                for attr_name in dir(obj):
                    if not attr_name.startswith('_'):  # Skip private attributes
                        try:
                            attr_value = getattr(obj, attr_name)
                            if not callable(attr_value):  # Skip methods
                                obj_dict[attr_name] = _sanitize_for_json(attr_value)
                        except Exception:
                            continue  # Skip problematic attributes
                return obj_dict if obj_dict else str(obj)
            except Exception as e:
                logger.warning(f"Error processing custom object: {e}")
                return f"<object: {type(obj).__name__}>"
        elif isinstance(obj, (int, float, bool)):
            # These are already JSON-safe
            return obj
        else:
            # For other types, try to convert to string safely
            try:
                str_repr = str(obj)
                # Verify the string representation is UTF-8 safe
                str_repr.encode('utf-8').decode('utf-8')
                return str_repr
            except Exception as e:
                logger.warning(f"Error converting {type(obj).__name__} to string: {e}")
                return f"<unsupported_type: {type(obj).__name__}>"
    except Exception as e:
        logger.error(f"Critical error in _sanitize_for_json: {e}")
        return f"<critical_sanitization_error: {type(obj).__name__}>"

# Import dependency functions from main
def get_key_manager() -> KeyManager:
    """Dependency to get key manager"""
    from backend.main import get_key_manager as _get_key_manager
    return _get_key_manager()

def get_ipfs_service() -> IPFSService:
    """Dependency to get IPFS service"""
    from backend.main import get_ipfs_service as _get_ipfs_service
    return _get_ipfs_service()

@router.post("/sign")
async def sign_record(
    record_data: Dict[str, Any] = Body(...),
    wallet_address: str = Body(...),
    key_manager: KeyManager = Depends(get_key_manager)
):
    """
    Doctor creates and signs a record, which is then returned to be encrypted and stored.
    """
    try:
        print(f"🔍 DEBUG: sign_record called with wallet_address: {wallet_address}")
        print(f"🔍 DEBUG: record_data type: {type(record_data)}")

        # Check if the wallet address matches the Doctor address
        if wallet_address == DOCTOR_ADDRESS:
            print(f"✅ Doctor {wallet_address} is signing a record")
        else:
            print(f"⚠️ Non-doctor address {wallet_address} is attempting to sign a record")

        # Validate the record data
        if not record_data:
            return safe_error_response("Record data is required", 400)

        # Sanitize input data first
        sanitized_record_data = _sanitize_for_json(record_data)
        print(f"🔍 DEBUG: sanitized_record_data type: {type(sanitized_record_data)}")

        # Create Merkle tree from the record data
        merkle_service = MerkleService()
        merkle_root, proofs = merkle_service.create_merkle_tree(sanitized_record_data)
        print(f"🔍 DEBUG: merkle_root type: {type(merkle_root)}, proofs type: {type(proofs)}")

        # Sign the merkle root with group signature using the doctor's member key
        signature = sign_message(merkle_root)
        print(f"🔍 DEBUG: signature type: {type(signature)}")

        # If group signature fails, fall back to a mock signature
        if signature is None:
            print("⚠️ Group signature failed. Using mock signature.")
            signature = hashlib.sha256(f"{merkle_root}_{int(time.time())}".encode()).hexdigest()

        # Prepare response data
        response_data = {
            "record": sanitized_record_data,
            "merkleRoot": merkle_root,
            "proofs": proofs,
            "signature": signature
        }

        print(f"🔍 DEBUG: response_data prepared, calling safe_success_response")
        return safe_success_response(data=response_data)

    except Exception as e:
        print(f"❌ Error in sign_record: {str(e)}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return safe_error_response(str(e), 500)

@router.post("/store")
async def store_record(
    data: Dict[str, Any] = Body(...),
    ipfs_service: IPFSService = Depends(get_ipfs_service),
    key_manager: KeyManager = Depends(get_key_manager)
):
    """
    Store an encrypted record on IPFS and register it on the blockchain
    """
    try:
        print(f"🔍 DEBUG: store_record called")
        print(f"🔍 DEBUG: input data type: {type(data)}")

        # Extract data and ensure all inputs are JSON-safe
        record = data.get("record", {})
        signature = data.get("signature", "")
        merkle_root = data.get("merkleRoot", "")
        patient_address = data.get("patientAddress", "")
        hospital_info = data.get("hospitalInfo", "General Hospital")

        print(f"📝 Storing record for patient {patient_address}")
        print(f"🔍 DEBUG: extracted data types - record: {type(record)}, signature: {type(signature)}")

        # Validate inputs
        if not record or not signature or not merkle_root or not patient_address:
            return safe_error_response("Missing required fields", 400)

        # Sanitize inputs to ensure they're JSON-safe
        record = _sanitize_for_json(record)
        signature = _sanitize_for_json(signature)
        merkle_root = _sanitize_for_json(merkle_root)
        patient_address = _sanitize_for_json(patient_address)
        hospital_info = _sanitize_for_json(hospital_info)

        print(f"🔍 DEBUG: sanitized inputs successfully")

        # Generate patient key deterministically (in production, use secure key derivation)
        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()
        print(f"🔍 DEBUG: generated patient_key: {len(patient_key)} bytes")

        # Encrypt the record with the patient's key
        encrypted_record = encrypt_record(record, patient_key)
        print(f"🔒 Encrypted record: {len(encrypted_record)} bytes")
        print(f"🔍 DEBUG: encrypted_record type: {type(encrypted_record)}")

        # Store encrypted record on IPFS
        cid = ipfs_service.add_data(encrypted_record)
        print(f"📦 Stored on IPFS with CID: {cid}")
        print(f"🔍 DEBUG: cid type: {type(cid)}")

        # Create eId (encrypted hospital info and patient key)
        # In production, this would use PCS encryption with Group Manager's public key
        eId = f"mock_eid_{hashlib.sha256(f'{hospital_info}_{patient_key.hex()}_{int(time.time())}'.encode()).hexdigest()}"
        print(f"🔍 DEBUG: eId type: {type(eId)}")

        # Get blockchain service and attempt real transaction
        blockchain_service = get_blockchain_service()
        print(f"🔍 DEBUG: blockchain_service type: {type(blockchain_service)}")

        if blockchain_service.is_connected():
            print("🔗 Attempting blockchain transaction...")

            # Get doctor's private key for signing
            import os
            doctor_private_key = os.getenv('DOCTOR_PRIVATE_KEY')

            if doctor_private_key:
                # Attempt real blockchain transaction
                tx_hash = blockchain_service.store_data(
                    cid=cid,
                    merkle_root=merkle_root,
                    signature=signature,
                    from_address=DOCTOR_ADDRESS,
                    private_key=doctor_private_key
                )

                # Validate and normalize transaction hash
                if tx_hash and blockchain_service.is_valid_tx_hash(tx_hash):
                    # Normalize the hash to ensure 0x prefix
                    tx_hash = blockchain_service.normalize_tx_hash(tx_hash)
                    print(f"✅ Real blockchain transaction: {tx_hash}")

                    # Wait for receipt (optional, can be done async)
                    try:
                        print(f"🔍 DEBUG: waiting for receipt for tx_hash: {tx_hash}")
                        receipt = blockchain_service.wait_for_receipt(tx_hash, timeout=60)
                        print(f"🔍 DEBUG: receipt type: {type(receipt)}")

                        gas_used = receipt.get('gasUsed', 200000) if receipt else 200000
                        gas_price = blockchain_service.get_gas_price()
                        print(f"🔍 DEBUG: gas_used: {gas_used}, gas_price: {gas_price}")

                        # Prepare response data
                        response_data = {
                            "cid": cid,
                            "merkleRoot": merkle_root,
                            "eId": eId,
                            "txHash": tx_hash,
                            "gasUsed": gas_used,
                            "gasPrice": gas_price,
                            "gasPriceGwei": gas_price / 1e9,
                            "simulated": False,
                            "receipt": receipt
                        }

                        print(f"🔍 DEBUG: calling safe_success_response with receipt")
                        return safe_success_response(data=response_data)
                    except Exception as receipt_error:
                        print(f"⚠️ Error getting receipt, but transaction was successful: {str(receipt_error)}")
                        print(f"🔍 DEBUG: receipt_error type: {type(receipt_error)}")

                        # Return success without receipt
                        gas_price = blockchain_service.get_gas_price()

                        # Prepare response data
                        response_data = {
                            "cid": cid,
                            "merkleRoot": merkle_root,
                            "eId": eId,
                            "txHash": tx_hash,
                            "gasUsed": 200000,  # Estimated
                            "gasPrice": gas_price,
                            "gasPriceGwei": gas_price / 1e9,
                            "simulated": False,
                            "receipt": None,
                            "receipt_error": str(receipt_error)
                        }

                        print(f"🔍 DEBUG: calling safe_success_response without receipt")
                        return safe_success_response(data=response_data)
                else:
                    print(f"⚠️ Blockchain transaction failed, using mock hash. Received: {tx_hash}")
                    print(f"   Expected: 0x + 64 hex characters (66 total), got: {len(tx_hash) if tx_hash else 0} characters")
            else:
                print("⚠️ Doctor private key not found, using mock transaction")
        else:
            print("⚠️ Blockchain not connected, using mock transaction")

        # Fallback to mock transaction
        tx_hash = f"0x{hashlib.sha256(f'{cid}_{merkle_root}_{int(time.time())}'.encode()).hexdigest()}"
        print(f"🔍 DEBUG: using mock tx_hash: {tx_hash}")

        # Prepare response data
        response_data = {
            "cid": cid,
            "merkleRoot": merkle_root,
            "eId": eId,
            "txHash": tx_hash,
            "gasUsed": 200000,  # Estimated
            "gasPrice": 10000000000,  # 10 Gwei
            "gasPriceGwei": 10.0,
            "simulated": True
        }

        print(f"🔍 DEBUG: calling safe_success_response with mock data")
        return safe_success_response(data=response_data)

    except Exception as e:
        print(f"❌ Error in store_record: {str(e)}")
        print(f"   Error type: {type(e).__name__}")

        # Check if this is a Unicode error and provide more details
        if isinstance(e, UnicodeDecodeError):
            print(f"   Unicode error details: {e}")
            print(f"   Position: {e.start}-{e.end}")
            print(f"   Object: {e.object[:50] if hasattr(e, 'object') else 'N/A'}")
            print(f"   Encoding: {e.encoding}")
            print(f"   Reason: {e.reason}")

        # Print full traceback for debugging
        import traceback
        traceback.print_exc()

        return safe_error_response(f"Store record error: {str(e)}", 500)

@router.get("/list")
async def list_patient_records(
    patient_address: str,
    ipfs_service: IPFSService = Depends(get_ipfs_service)
):
    """
    List all records for a patient
    """
    try:
        # Check if the wallet address matches the Patient address
        if patient_address == PATIENT_ADDRESS:
            print(f"✅ Patient {patient_address} is listing their records")
        else:
            print(f"⚠️ Non-patient address {patient_address} is attempting to list records")

        records = []

        # Check local storage for records
        import os
        from backend.config import LOCAL_STORAGE_PATH

        if os.path.exists(LOCAL_STORAGE_PATH):
            for filename in os.listdir(LOCAL_STORAGE_PATH):
                file_path = os.path.join(LOCAL_STORAGE_PATH, filename)
                if os.path.isfile(file_path):
                    try:
                        # Try to decrypt the record
                        with open(file_path, "rb") as f:
                            encrypted_record = f.read()

                        # Generate the patient's key deterministically
                        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()

                        # Decrypt the record (for API response, keep binary data as base64)
                        decrypted_record = decrypt_record(encrypted_record, patient_key, for_api_response=True)

                        # Check if this record belongs to this patient
                        patient_id = decrypted_record.get("patientId") or decrypted_record.get("patientID")

                        if patient_id == patient_address:
                            # Add metadata to the record
                            decrypted_record["cid"] = filename
                            decrypted_record["timestamp"] = os.path.getmtime(file_path)
                            records.append(decrypted_record)
                            print(f"✅ Added record {filename} to patient's records")
                    except Exception as e:
                        # Skip records that can't be decrypted with this patient's key
                        print(f"⚠️ Skipping record {filename}: {str(e)}")

        return safe_success_response(data=records)
    except Exception as e:
        print(f"❌ Error in list_patient_records: {str(e)}")
        import traceback
        traceback.print_exc()
        return safe_error_response(str(e), 500)

@router.post("/retrieve")
async def retrieve_record(
    data: Dict[str, Any] = Body(...),
    ipfs_service: IPFSService = Depends(get_ipfs_service),
    key_manager: KeyManager = Depends(get_key_manager)
):
    """
    Patient retrieves and decrypts a record using their key
    """
    try:
        # Extract data
        cid = data.get("cid", "")
        signature = data.get("signature", "")
        eId = data.get("eId", "")
        patient_address = data.get("patientAddress", "")

        # Validate inputs
        if not cid or not patient_address or not eId or not signature:
            return error_response("Missing required fields", 400)

        print(f"📖 Patient {patient_address} retrieving record {cid}")

        # Generate merkle_root deterministically for demo
        merkle_root = hashlib.sha256(f"{cid}_merkle_root".encode()).hexdigest()

        # Verify the signature
        signature_verified = verify_signature(merkle_root, signature)
        if not signature_verified:
            print(f"❌ Signature verification failed")
            return error_response("Invalid signature", 400)

        print(f"✅ Signature verified successfully")

        # Retrieve the encrypted record
        encrypted_record = ipfs_service.get_data(cid)
        if not encrypted_record:
            return error_response("Record not found", 404)

        # Generate patient key (in production, derive securely)
        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()

        # Decrypt the record (for API response, keep binary data as base64)
        decrypted_record = decrypt_record(encrypted_record, patient_key, for_api_response=True)

        return safe_success_response(data=decrypted_record)
    except Exception as e:
        print(f"❌ Error in retrieve_record: {str(e)}")
        import traceback
        traceback.print_exc()
        return safe_error_response(str(e), 500)
