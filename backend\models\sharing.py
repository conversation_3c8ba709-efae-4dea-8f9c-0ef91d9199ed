"""
Sharing-related Pydantic models
"""
from pydantic import BaseModel
from typing import Optional

class ShareRequest(BaseModel):
    """Model for record sharing request"""
    record_cid: str
    doctor_address: str
    wallet_address: str

class ShareResponse(BaseModel):
    """Model for record sharing response"""
    shared_cid: str
    encrypted_key: str
    doctor_address: str
    success: bool = True
    message: Optional[str] = None

class DecryptRequest(BaseModel):
    """Model for record decryption request"""
    shared_cid: str
    encrypted_key: str
    wallet_address: str
