version: '3.8'

services:
<<<<<<< HEAD
  # Streamlit Web UI
  web:
=======
  healthcare-web:
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c
    build:
      context: .
      dockerfile: Dockerfile
    container_name: healthcare-web
<<<<<<< HEAD
=======
    hostname: web
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c
    ports:
      - "0.0.0.0:8501:8501"  # Streamlit UI - accessible externally
    volumes:
      - ./app:/app/app
      - ./backend:/app/backend
      - ./pygroupsig:/app/pygroupsig
      - ./artifacts:/app/artifacts
      - ./local_storage:/app/local_storage
      - ./keys:/app/keys
    depends_on:
<<<<<<< HEAD
      api:
        condition: service_started
=======
      - healthcare-api
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c
    environment:
      - API_URL=http://healthcare-api:8000/api
      - PYTHONPATH=/app
      - PRIVATE_KEY=${PRIVATE_KEY:-91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e}
      - SEPOLIA_RPC_URL=${SEPOLIA_RPC_URL:-https://base-sepolia-rpc.publicnode.com}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-0x8Cbf9a04C9c7F329DCcaeabE90a424e8F9687aaA}
      - DOCTOR_PRIVATE_KEY=${DOCTOR_PRIVATE_KEY:-0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80}
      - PATIENT_PRIVATE_KEY=${PATIENT_PRIVATE_KEY:-0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d}
      - HOSPITAL_PRIVATE_KEY=${HOSPITAL_PRIVATE_KEY:-0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a}
      - GROUP_MANAGER_PRIVATE_KEY=${GROUP_MANAGER_PRIVATE_KEY:-0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6}
      - REVOCATION_MANAGER_PRIVATE_KEY=${REVOCATION_MANAGER_PRIVATE_KEY:-0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a}
      - BUYER_PRIVATE_KEY=${BUYER_PRIVATE_KEY:-0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba}
    networks:
      - healthcare-network
    restart: unless-stopped
    command: streamlit run app/main.py
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8501 || exit 0"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

<<<<<<< HEAD
  # FastAPI Backend
  api:
=======
  healthcare-api:
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c
    build:
      context: .
      dockerfile: Dockerfile
    container_name: healthcare-api
<<<<<<< HEAD
=======
    hostname: api
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c
    ports:
      - "0.0.0.0:8000:8000"  # FastAPI - accessible externally
    volumes:
      - ./backend:/app/backend
      - ./pygroupsig:/app/pygroupsig
      - ./artifacts:/app/artifacts
      - ./local_storage:/app/local_storage
<<<<<<< HEAD
      - ./keys:/app/keys
      - ./ipfs-data:/ipfs-data  # Mount IPFS data for direct access
=======
      - ./test_contract.py:/app/test_contract.py
      - ./keys:/app/keys
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c
    depends_on:
      ipfs:
        condition: service_started
    environment:
      - IPFS_URL=http://ipfs:5001
      - IPFS_API_HOST=ipfs
      - IPFS_API_PORT=5001
      - IPFS_PATH=/ipfs-data  # Set IPFS path for CLI commands
      - SEPOLIA_RPC_URL=${SEPOLIA_RPC_URL:-https://base-sepolia-rpc.publicnode.com}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-0x8Cbf9a04C9c7F329DCcaeabE90a424e8F9687aaA}
      - PRIVATE_KEY=${PRIVATE_KEY:-91e5c2bed81b69f9176b6404710914e9bf36a6359122a2d1570116fc6322562e}
      - MCL_LIB_PATH=/usr/local/lib/mcl
      - PYTHONPATH=/app
      - DOCTOR_PRIVATE_KEY=${DOCTOR_PRIVATE_KEY:-0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80}
      - PATIENT_PRIVATE_KEY=${PATIENT_PRIVATE_KEY:-0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d}
      - HOSPITAL_PRIVATE_KEY=${HOSPITAL_PRIVATE_KEY:-0x5de4111afa1a4b94908f83103eb1f1706367c2e68ca870fc3fb9a804cdab365a}
      - GROUP_MANAGER_PRIVATE_KEY=${GROUP_MANAGER_PRIVATE_KEY:-0x7c852118294e51e653712a81e05800f419141751be58f605c371e15141b007a6}
      - REVOCATION_MANAGER_PRIVATE_KEY=${REVOCATION_MANAGER_PRIVATE_KEY:-0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a}
      - BUYER_PRIVATE_KEY=${BUYER_PRIVATE_KEY:-0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba}
      - BASESCAN_API_KEY=${BASESCAN_API_KEY:-I61T8UZK7YKRC8P61BHF6237PG9GC2VK3Y}
    networks:
      - healthcare-network
    restart: unless-stopped
<<<<<<< HEAD
    command: >
      sh -c "
        # Wait for IPFS to be fully ready
        echo 'Waiting for IPFS to be ready...'
        sleep 15
=======
    command: bash -c "python /app/check_keys.py && uvicorn backend.api:app --host 0.0.0.0 --port 8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
>>>>>>> d1210d8e0a8ca8de4644cff25d490c9c1288ed1c

        # Create necessary directories
        mkdir -p /app/local_storage/records
        mkdir -p /app/local_storage/purchases
        mkdir -p /app/local_storage/transactions
        mkdir -p /app/local_storage/store_transactions
        mkdir -p /app/local_storage/share_transactions
        mkdir -p /app/local_storage/purchase_transactions

        # Check if MCL library is properly installed
        echo 'Checking MCL library...'
        ls -la /usr/local/lib/mcl || true
        echo 'Checking MCL library in alternate location...'
        ls -la /app/mcl/lib || true
        echo 'Checking MCL library in build location...'
        ls -la /app/mcl/build/lib || true

        # Install IPFS CLI if not already installed
        if ! command -v ipfs &> /dev/null; then
          echo 'Installing IPFS CLI...'
          apt-get update && apt-get install -y wget
          wget -q https://dist.ipfs.tech/kubo/v0.22.0/kubo_v0.22.0_linux-amd64.tar.gz
          tar -xzf kubo_v0.22.0_linux-amd64.tar.gz
          cp kubo/ipfs /usr/local/bin/
          rm -rf kubo_v0.22.0_linux-amd64.tar.gz kubo
        fi

        # Start the API server
        echo 'Starting API server...'
        # Use a simple approach without reload to ensure stability
        uvicorn backend.api:app --host 0.0.0.0 --port 8000
      "
    healthcheck:
      test: ["CMD-SHELL", "curl -s http://localhost:8000/ || exit 0"]
      interval: 60s
      timeout: 30s
      retries: 10
      start_period: 120s

  # IPFS Node
  ipfs:
    image: ipfs/kubo:v0.22.0
    container_name: ipfs-node
    ports:
      - "0.0.0.0:4001:4001"  # IPFS swarm - accessible externally
      - "0.0.0.0:5001:5001"  # IPFS API - accessible externally
      - "0.0.0.0:8080:8080"  # IPFS Gateway - accessible externally
    volumes:
      - ./ipfs-data:/data/ipfs
      - ./local_storage:/export
    environment:
      - IPFS_PROFILE=server
      # Fix permissions issues
      - IPFS_PATH=/data/ipfs
    networks:
      - healthcare-network
    restart: unless-stopped
    entrypoint: ["/sbin/tini", "--", "/usr/local/bin/start_ipfs"]
    command: ["daemon", "--migrate=true", "--enable-gc"]
    user: "1000:1000"  # Use a specific user ID to avoid permission issues
    healthcheck:
      test: ["CMD-SHELL", "ipfs --api=/ip4/127.0.0.1/tcp/5001 id || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  healthcare-network:
    driver: bridge
