#!/usr/bin/env python3
"""
Test script to verify the FastAPI backend fix for binary data encoding issue
"""
import requests
import json
import sys
import time

# Test configuration
API_BASE_URL = "http://localhost:8000"
PATIENT_ADDRESS = "0xEDB64f85F1fC9357EcA100C2970f7F84a5faAD4A"

def test_health_endpoint():
    """Test the health endpoint to ensure the API is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health endpoint working")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {str(e)}")
        return False

def test_records_list_endpoint():
    """Test the problematic /records/list endpoint"""
    try:
        print(f"🔍 Testing /records/list endpoint with patient address: {PATIENT_ADDRESS}")
        
        # Test both endpoint variations
        endpoints = ["/records/list", "/api/records/list"]
        
        for endpoint in endpoints:
            print(f"\n📡 Testing endpoint: {endpoint}")
            
            response = requests.get(
                f"{API_BASE_URL}{endpoint}",
                params={"patient_address": PATIENT_ADDRESS}
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ {endpoint} - JSON response received successfully")
                    print(f"Response data type: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"Response keys: {list(data.keys())}")
                        if 'data' in data:
                            records = data['data']
                            print(f"Number of records: {len(records) if isinstance(records, list) else 'N/A'}")
                            
                            # Check if any records contain binary data that's properly encoded
                            if isinstance(records, list) and records:
                                print("Sample record structure:")
                                for i, record in enumerate(records[:2]):  # Show first 2 records
                                    print(f"  Record {i+1}: {type(record)} with keys: {list(record.keys()) if isinstance(record, dict) else 'N/A'}")
                    
                    return True
                    
                except json.JSONDecodeError as e:
                    print(f"❌ {endpoint} - JSON decode error: {str(e)}")
                    print(f"Raw response: {response.text[:500]}...")
                    return False
                    
            elif response.status_code == 500:
                print(f"❌ {endpoint} - Internal Server Error")
                try:
                    error_data = response.json()
                    print(f"Error details: {error_data}")
                except:
                    print(f"Raw error response: {response.text[:500]}...")
                return False
                
            else:
                print(f"❌ {endpoint} - Unexpected status code: {response.status_code}")
                print(f"Response: {response.text[:500]}...")
                return False
                
    except Exception as e:
        print(f"❌ Records list endpoint error: {str(e)}")
        return False

def test_fees_endpoints():
    """Test the fees endpoints"""
    try:
        print(f"\n🔍 Testing fees endpoints")

        endpoints = ["/fees", "/api/fees"]

        for endpoint in endpoints:
            print(f"\n📡 Testing endpoint: {endpoint}")

            response = requests.get(f"{API_BASE_URL}{endpoint}")

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ {endpoint} - JSON response received successfully")
                    print(f"Response keys: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")

                    # Check if it has the expected structure
                    if isinstance(data, dict):
                        expected_keys = ["total_gas_fees", "transaction_count", "average_gas_fee"]
                        if all(key in data for key in expected_keys):
                            print(f"✅ {endpoint} - Response has expected structure")
                            return True
                        else:
                            print(f"⚠️ {endpoint} - Response missing expected keys")

                    return True
                except json.JSONDecodeError as e:
                    print(f"❌ {endpoint} - JSON decode error: {str(e)}")
                    return False
            elif response.status_code == 404:
                print(f"❌ {endpoint} - Not Found (404)")
                return False
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                return False

    except Exception as e:
        print(f"❌ Fees endpoint error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting FastAPI Backend Fix Test")
    print("=" * 50)
    print("📝 Make sure to run the FastAPI server from the PROJECT ROOT with:")
    print("   wsl -d Ubuntu -e bash -c \"cd /mnt/c/Users/<USER>/project/pygroupsig && source venv/bin/activate && python -m uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000\"")
    print("=" * 50)

    # Wait a moment for the server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(3)
    
    # Test health endpoint first
    if not test_health_endpoint():
        print("\n❌ Health check failed. Make sure the FastAPI server is running.")
        sys.exit(1)
    
    # Test the main problematic endpoint
    records_success = test_records_list_endpoint()
    
    # Test fees endpoints
    fees_success = test_fees_endpoints()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"  Records List Endpoint: {'✅ PASS' if records_success else '❌ FAIL'}")
    print(f"  Fees Endpoints: {'✅ PASS' if fees_success else '❌ FAIL'}")
    
    if records_success and fees_success:
        print("\n🎉 All tests passed! The binary data encoding fix is working.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
