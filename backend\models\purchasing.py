"""
Purchasing-related Pydantic models
"""
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

class PurchaseRequest(BaseModel):
    """Model for purchase request"""
    template_hash: str
    amount: float
    template: Optional[Dict[str, Any]] = None

class PurchaseResponse(BaseModel):
    """Model for purchase response"""
    request_id: str
    status: str
    template_cid: Optional[str] = None
    cert_cid: Optional[str] = None

class TemplateData(BaseModel):
    """Model for template data"""
    template_id: str
    fields: Dict[str, Any]
    requirements: List[str]

class BuyerRequest(BaseModel):
    """Model for buyer purchase request"""
    template: Dict[str, Any]
    wallet_address: str

class HospitalConfirmation(BaseModel):
    """Model for hospital confirmation"""
    request_id: str
    wallet_address: str
    confirmed: bool = True

class PatientFillTemplate(BaseModel):
    """Model for patient template filling"""
    request_id: str
    wallet_address: str

class PurchaseVerification(BaseModel):
    """Model for purchase verification"""
    request_id: str
    wallet_address: str
    template_cid: str
