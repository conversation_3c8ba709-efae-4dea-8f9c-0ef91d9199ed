"""
Helper utility functions
"""
import hashlib
import time
from datetime import datetime
from typing import Optional

def clean_cid(cid: str) -> str:
    """
    Clean CID by removing quotes and whitespace
    
    Args:
        cid: The CID to clean
        
    Returns:
        str: Cleaned CID
    """
    if not cid:
        return cid
    return cid.strip().strip('"').strip("'")

def generate_request_id(prefix: str = "req", wallet_address: Optional[str] = None) -> str:
    """
    Generate a unique request ID
    
    Args:
        prefix: Prefix for the request ID
        wallet_address: Optional wallet address to include in hash
        
    Returns:
        str: Unique request ID
    """
    timestamp = int(time.time())
    
    if wallet_address:
        hash_input = f"{wallet_address}_{timestamp}".encode()
        hash_suffix = hashlib.sha256(hash_input).hexdigest()[:8]
        return f"{prefix}_{timestamp}_{hash_suffix}"
    else:
        return f"{prefix}_{timestamp}"

def format_timestamp(timestamp: Optional[float] = None) -> str:
    """
    Format timestamp to ISO string
    
    Args:
        timestamp: Unix timestamp (defaults to current time)
        
    Returns:
        str: ISO formatted timestamp
    """
    if timestamp is None:
        timestamp = time.time()
    
    return datetime.fromtimestamp(timestamp).isoformat() + "Z"

def validate_wallet_address(address: str) -> bool:
    """
    Basic validation for Ethereum wallet address
    
    Args:
        address: Wallet address to validate
        
    Returns:
        bool: True if valid format
    """
    if not address:
        return False
    
    # Basic Ethereum address validation
    if not address.startswith("0x"):
        return False
    
    if len(address) != 42:
        return False
    
    try:
        int(address[2:], 16)
        return True
    except ValueError:
        return False

def calculate_file_hash(data: bytes, algorithm: str = "sha256") -> str:
    """
    Calculate hash of file data
    
    Args:
        data: File data as bytes
        algorithm: Hash algorithm to use
        
    Returns:
        str: Hex encoded hash
    """
    if algorithm == "sha256":
        return hashlib.sha256(data).hexdigest()
    elif algorithm == "md5":
        return hashlib.md5(data).hexdigest()
    else:
        raise ValueError(f"Unsupported hash algorithm: {algorithm}")

def truncate_string(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    Truncate string to maximum length
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        suffix: Suffix to add when truncated
        
    Returns:
        str: Truncated string
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix
