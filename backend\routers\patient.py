"""
Patient-specific endpoints
"""
import os
import json
import time
from fastapi import APIRouter, Body
from typing import List

from backend.config import success_response, error_response, LOCAL_STORAGE_PATH, PATIENT_ADDRESS

router = APIRouter()

@router.get("/patient/requests")
async def get_patient_requests(wallet_address: str):
    """
    Get data requests for a patient
    """
    try:
        print(f"Getting data requests for patient {wallet_address}")

        # For demo purposes, we'll look for purchase requests that have been replied to by the hospital
        # In a real implementation, we would filter requests based on the patient's data

        # Check if the local storage directory exists
        purchases_dir = os.path.join(LOCAL_STORAGE_PATH, "purchases")
        if not os.path.exists(purchases_dir):
            return success_response(data={"requests": []})

        # Get all purchase files
        purchase_files = os.listdir(purchases_dir)

        # Filter for requests that have been replied to by the hospital
        requests = []
        for file_name in purchase_files:
            if not file_name.endswith(".json"):
                continue

            file_path = os.path.join(purchases_dir, file_name)

            try:
                with open(file_path, "r") as f:
                    purchase_data = json.load(f)

                # Check if this request has been replied to by the hospital
                if purchase_data.get("status") in ["hospital_replied", "pending_patient_data"]:
                    request_info = {
                        "request_id": purchase_data.get("request_id"),
                        "buyer_address": purchase_data.get("buyer_address"),
                        "hospital_address": purchase_data.get("hospital_address"),
                        "status": purchase_data.get("status"),
                        "template": purchase_data.get("template", {}),
                        "hospital_reply": purchase_data.get("hospital_reply", {}),
                        "created_at": purchase_data.get("created_at"),
                        "updated_at": purchase_data.get("updated_at")
                    }
                    requests.append(request_info)
            except Exception as e:
                print(f"Error processing file {file_name}: {str(e)}")

        # Sort by creation time (newest first)
        requests.sort(key=lambda x: x.get("created_at", 0), reverse=True)

        return success_response(data={"requests": requests})
    except Exception as e:
        print(f"Error in get_patient_requests: {str(e)}")
        return error_response(str(e), 500)

@router.post("/store")
async def store_record(
    cid: str,
    merkle_root: str,
    signature: str,
    wallet_address: str
):
    """
    Patient stores the record on IPFS and registers it on the blockchain
    """
    try:
        # Check if the wallet address matches the Patient address
        if wallet_address == PATIENT_ADDRESS:
            print(f"✅ Patient {wallet_address} storing record {cid} with merkle root {merkle_root}")
        else:
            print(f"⚠️ Non-patient address {wallet_address} attempting to store a record")

        # For demo purposes, create a mock transaction
        # In production, this would interact with the blockchain
        
        # Generate a mock transaction hash
        import hashlib
        tx_hash = f"0x{hashlib.sha256(f'{cid}_{merkle_root}_{int(time.time())}'.encode()).hexdigest()}"
        
        # Create transaction record
        transaction = {
            "id": f"store_{int(time.time())}",
            "type": "Store Record",
            "status": "Completed",
            "timestamp": int(time.time()),
            "gas_fee": 0.001,  # Estimated gas fee
            "wallet_address": wallet_address,
            "details": {
                "cid": cid,
                "merkle_root": merkle_root,
                "signature": signature,
                "tx_hash": tx_hash
            },
            "workflow": "storing"
        }

        # Save transaction to local storage
        os.makedirs("local_storage/storing_transactions", exist_ok=True)
        tx_file = f"local_storage/storing_transactions/{transaction['id']}.json"
        with open(tx_file, "w") as f:
            json.dump(transaction, f, indent=2)

        print(f"✅ Record stored with transaction hash: {tx_hash}")

        return success_response(
            data={
                "cid": cid,
                "merkle_root": merkle_root,
                "tx_hash": tx_hash,
                "gas_used": 200000,  # Estimated
                "gas_price": 10000000000,  # 10 Gwei
                "gas_fee": 0.001,
                "simulated": True,
                "message": "Record stored successfully (simulated)"
            }
        )
    except Exception as e:
        return error_response(str(e), 500)

@router.post("/revocation/request")
async def request_revocation(
    request_id: str,
    template_cid: str,
    signature: str,
    wallet_address: str
):
    """
    Request revocation of a doctor's signature
    """
    try:
        print(f"🚫 Revocation request from {wallet_address} for template {template_cid}")

        # Create revocation request
        revocation_request = {
            "revocation_id": f"rev_{int(time.time())}",
            "request_id": request_id,
            "template_cid": template_cid,
            "signature": signature,
            "requester": wallet_address,
            "status": "pending",
            "created_at": time.time(),
            "reason": "Data verification failed"
        }

        # Save revocation request
        os.makedirs("local_storage/revocations", exist_ok=True)
        rev_file = f"local_storage/revocations/{revocation_request['revocation_id']}.json"
        with open(rev_file, "w") as f:
            json.dump(revocation_request, f, indent=2)

        print(f"✅ Revocation request created: {revocation_request['revocation_id']}")

        return success_response(
            data={
                "revocation_id": revocation_request["revocation_id"],
                "status": "pending",
                "message": "Revocation request submitted successfully"
            }
        )
    except Exception as e:
        return error_response(str(e), 500)
