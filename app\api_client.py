"""
API client for connecting to the refactored Healthcare Data Sharing API
"""
import requests
import json
import os
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class HealthcareAPIClient:
    """Client for interacting with the refactored Healthcare Data Sharing API"""
    
    def __init__(self, base_url: str = None):
        """Initialize the API client
        
        Args:
            base_url: Base URL for the API (defaults to environment variable or localhost)
        """
        self.base_url = base_url or os.getenv("API_URL", "http://127.0.0.1:8000")
        # Remove /api suffix if present since the new API has different routing
        if self.base_url.endswith("/api"):
            self.base_url = self.base_url[:-4]
        
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make a request to the API
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            **kwargs: Additional arguments for requests
            
        Returns:
            requests.Response: The response object
        """
        url = f"{self.base_url}{endpoint}"
        print(f"Making {method} request to: {url}")
        
        try:
            response = self.session.request(method, url, **kwargs)
            print(f"Response status: {response.status_code}")
            return response
        except Exception as e:
            print(f"Request failed: {str(e)}")
            raise
    
    # Health endpoints
    def health_check(self) -> Dict[str, Any]:
        """Check API health"""
        response = self._make_request("GET", "/health")
        return response.json() if response.status_code == 200 else None
    
    # Authentication endpoints
    def get_auth_challenge(self, wallet_address: str) -> Dict[str, Any]:
        """Get authentication challenge"""
        response = self._make_request("POST", "/auth/challenge", 
                                    json={"wallet_address": wallet_address})
        return response.json() if response.status_code == 200 else None
    
    def verify_auth(self, wallet_address: str, signature: str) -> Dict[str, Any]:
        """Verify authentication signature"""
        response = self._make_request("POST", "/auth/verify", 
                                    json={"wallet_address": wallet_address, "signature": signature})
        return response.json() if response.status_code == 200 else None
    
    def logout(self, wallet_address: str) -> Dict[str, Any]:
        """Logout user"""
        response = self._make_request("POST", "/auth/logout", 
                                    json={"wallet_address": wallet_address})
        return response.json() if response.status_code == 200 else None
    
    def get_auth_status(self, wallet_address: str) -> Dict[str, Any]:
        """Get authentication status"""
        response = self._make_request("GET", "/auth/status", 
                                    params={"wallet_address": wallet_address})
        return response.json() if response.status_code == 200 else None
    
    # Records endpoints
    def sign_record(self, record_data: Dict[str, Any], wallet_address: str) -> Dict[str, Any]:
        """Sign a medical record"""
        response = self._make_request("POST", "/records/sign", 
                                    json={"record_data": record_data, "wallet_address": wallet_address})
        return response.json() if response.status_code == 200 else None
    
    def store_record(self, record: Dict[str, Any], signature: str, merkle_root: str, 
                    patient_address: str, hospital_info: str = "General Hospital") -> Dict[str, Any]:
        """Store a medical record"""
        response = self._make_request("POST", "/records/store", 
                                    json={
                                        "record": record,
                                        "signature": signature,
                                        "merkleRoot": merkle_root,
                                        "patientAddress": patient_address,
                                        "hospitalInfo": hospital_info
                                    })
        return response.json() if response.status_code == 200 else None
    
    def list_patient_records(self, patient_address: str) -> Dict[str, Any]:
        """List all records for a patient"""
        response = self._make_request("GET", "/records/list", 
                                    params={"patient_address": patient_address})
        return response.json() if response.status_code == 200 else None
    
    def retrieve_record(self, cid: str, signature: str, eId: str, patient_address: str) -> Dict[str, Any]:
        """Retrieve and decrypt a record"""
        response = self._make_request("POST", "/records/retrieve", 
                                    json={
                                        "cid": cid,
                                        "signature": signature,
                                        "eId": eId,
                                        "patientAddress": patient_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    # Sharing endpoints
    def share_record(self, record_cid: str, doctor_address: str, wallet_address: str) -> Dict[str, Any]:
        """Share a record with a doctor"""
        response = self._make_request("POST", "/share", 
                                    json={
                                        "record_cid": record_cid,
                                        "doctor_address": doctor_address,
                                        "wallet_address": wallet_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    def decrypt_shared_record(self, shared_cid: str, encrypted_key: str, wallet_address: str) -> Dict[str, Any]:
        """Decrypt a shared record"""
        response = self._make_request("POST", "/decrypt", 
                                    json={
                                        "shared_cid": shared_cid,
                                        "encrypted_key": encrypted_key,
                                        "wallet_address": wallet_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    # Purchasing endpoints
    def create_purchase_request(self, template: Dict[str, Any], wallet_address: str) -> Dict[str, Any]:
        """Create a purchase request"""
        response = self._make_request("POST", "/purchase/request", 
                                    json={
                                        "template": template,
                                        "wallet_address": wallet_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    def confirm_purchase_request(self, request_id: str, wallet_address: str, confirmed: bool = True) -> Dict[str, Any]:
        """Hospital confirms a purchase request"""
        response = self._make_request("POST", "/purchase/confirm", 
                                    json={
                                        "request_id": request_id,
                                        "wallet_address": wallet_address,
                                        "confirmed": confirmed
                                    })
        return response.json() if response.status_code == 200 else None
    
    def get_purchase_requests(self, wallet_address: str) -> Dict[str, Any]:
        """Get purchase requests filtered by role"""
        response = self._make_request("GET", "/purchase/requests", 
                                    params={"wallet_address": wallet_address})
        return response.json() if response.status_code == 200 else None
    
    def fill_template(self, request_id: str, wallet_address: str) -> Dict[str, Any]:
        """Patient fills a template for a purchase request"""
        response = self._make_request("POST", "/patient/fill-template", 
                                    json={
                                        "request_id": request_id,
                                        "wallet_address": wallet_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    # Templates endpoints
    def get_template(self, template_cid: str, wallet_address: str = None) -> Dict[str, Any]:
        """Get template data"""
        params = {"wallet_address": wallet_address} if wallet_address else {}
        response = self._make_request("GET", f"/template/{template_cid}", params=params)
        return response.json() if response.status_code == 200 else None
    
    def verify_purchase_template(self, request_id: str, wallet_address: str, template_cid: str) -> Dict[str, Any]:
        """Verify a purchase template"""
        response = self._make_request("POST", "/purchase/verify", 
                                    json={
                                        "request_id": request_id,
                                        "wallet_address": wallet_address,
                                        "template_cid": template_cid
                                    })
        return response.json() if response.status_code == 200 else None
    
    def list_templates(self, wallet_address: str = None) -> Dict[str, Any]:
        """List available templates"""
        params = {"wallet_address": wallet_address} if wallet_address else {}
        response = self._make_request("GET", "/templates/list", params=params)
        return response.json() if response.status_code == 200 else None
    
    # Verification endpoints
    def verify_content(self, cid: str) -> Dict[str, Any]:
        """Verify if content exists in IPFS or local storage"""
        response = self._make_request("GET", f"/verify/{cid}")
        return response.json() if response.status_code == 200 else None
    
    def compute_partial_opening(self, opening_id: int, signature: str, manager_type: str, wallet_address: str) -> Dict[str, Any]:
        """Compute partial opening for signature verification"""
        response = self._make_request("POST", "/opening/compute_partial", 
                                    json={
                                        "opening_id": opening_id,
                                        "signature": signature,
                                        "manager_type": manager_type,
                                        "wallet_address": wallet_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    def request_opening(self, signature: str, reason: str, wallet_address: str) -> Dict[str, Any]:
        """Request signature opening"""
        response = self._make_request("POST", "/opening/request", 
                                    json={
                                        "signature": signature,
                                        "reason": reason,
                                        "wallet_address": wallet_address
                                    })
        return response.json() if response.status_code == 200 else None
    
    def get_opening_status(self, request_id: int) -> Dict[str, Any]:
        """Get opening request status"""
        response = self._make_request("GET", f"/opening/status/{request_id}")
        return response.json() if response.status_code == 200 else None


# Global API client instance
api_client = HealthcareAPIClient()
