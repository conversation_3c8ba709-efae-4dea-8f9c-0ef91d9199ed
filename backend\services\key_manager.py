"""
Key management service for handling RSA key pairs
"""
import os
from typing import Di<PERSON>, <PERSON><PERSON>, Optional
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend

from backend.config import (
    SECURE_KEYS_PATH,
    PATIENT_ADDRESS,
    DOCTOR_ADDRESS,
    HOSPITAL_ADDRESS,
    BUYER_ADDRESS,
    GROUP_MANAGER_ADDRESS,
    REVOCATION_MANAGER_ADDRESS
)

class KeyManager:
    """Manages RSA key pairs for all system roles"""
    
    def __init__(self):
        self.key_store: Dict[str, rsa.RSAPrivateKey] = {}
        self.public_key_store: Dict[str, rsa.RSAPublicKey] = {}
        self._initialize_keys()
    
    def _initialize_keys(self):
        """Initialize keys for all roles"""
        try:
            # Ensure secure keys directory exists
            os.makedirs(SECURE_KEYS_PATH, exist_ok=True)
            
            # Generate or load keys for each role
            self._initialize_role_keys("patient", PATIENT_ADDRESS)
            self._initialize_role_keys("doctor", DOCTOR_ADDRESS)
            self._initialize_role_keys("hospital", HOSPITAL_ADDRESS)
            self._initialize_role_keys("buyer", BUYER_ADDRESS)
            self._initialize_role_keys("group_manager", GROUP_MANAGER_ADDRESS)
            self._initialize_role_keys("revocation_manager", REVOCATION_MANAGER_ADDRESS)
            
            print("✅ Key initialization complete")
        except Exception as e:
            print(f"❌ Error initializing keys: {str(e)}")
            # Fall back to in-memory keys for demo
            self._generate_fallback_keys()
    
    def _initialize_role_keys(self, role: str, address: str):
        """Initialize keys for a specific role"""
        key_file = f"{SECURE_KEYS_PATH}/{role}_{address}.pem"
        pub_key_file = f"{SECURE_KEYS_PATH}/{role}_{address}_pub.pem"
        
        if os.path.exists(key_file) and os.path.exists(pub_key_file):
            # Load existing keys
            try:
                with open(key_file, "rb") as f:
                    private_key = serialization.load_pem_private_key(
                        f.read(),
                        password=None,
                        backend=default_backend()
                    )
                
                with open(pub_key_file, "rb") as f:
                    public_key = serialization.load_pem_public_key(
                        f.read(),
                        backend=default_backend()
                    )
                
                self.key_store[address] = private_key
                self.public_key_store[address] = public_key
                print(f"✅ Loaded existing keys for {role} ({address})")
            except Exception as e:
                print(f"❌ Error loading keys for {role}: {str(e)}")
                self._generate_and_save_keys(role, address)
        else:
            # Generate new keys
            self._generate_and_save_keys(role, address)
    
    def _generate_and_save_keys(self, role: str, address: str):
        """Generate and save new keys for a role"""
        private_key, public_key = self.generate_rsa_key_pair()
        
        # Save private key
        key_file = f"{SECURE_KEYS_PATH}/{role}_{address}.pem"
        with open(key_file, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        # Save public key
        pub_key_file = f"{SECURE_KEYS_PATH}/{role}_{address}_pub.pem"
        with open(pub_key_file, "wb") as f:
            f.write(public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            ))
        
        # Store in memory
        self.key_store[address] = private_key
        self.public_key_store[address] = public_key
        print(f"✅ Generated and saved new keys for {role} ({address})")
    
    def _generate_fallback_keys(self):
        """Generate fallback keys for all roles"""
        for role, address in [
            ("patient", PATIENT_ADDRESS),
            ("doctor", DOCTOR_ADDRESS),
            ("hospital", HOSPITAL_ADDRESS),
            ("buyer", BUYER_ADDRESS),
            ("group_manager", GROUP_MANAGER_ADDRESS),
            ("revocation_manager", REVOCATION_MANAGER_ADDRESS)
        ]:
            private_key, public_key = self.generate_rsa_key_pair()
            self.key_store[address] = private_key
            self.public_key_store[address] = public_key
            print(f"✅ Generated fallback keys for {role} ({address})")
    
    @staticmethod
    def generate_rsa_key_pair() -> Tuple[rsa.RSAPrivateKey, rsa.RSAPublicKey]:
        """Generate a new RSA key pair"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=default_backend()
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    def get_private_key(self, address: str) -> Optional[rsa.RSAPrivateKey]:
        """Get private key for an address"""
        return self.key_store.get(address)
    
    def get_public_key(self, address: str) -> Optional[rsa.RSAPublicKey]:
        """Get public key for an address"""
        return self.public_key_store.get(address)
    
    def encrypt_with_public_key(self, data: bytes, address: str) -> bytes:
        """Encrypt data with public key for an address"""
        public_key = self.get_public_key(address)
        if not public_key:
            raise ValueError(f"No public key found for address: {address}")
        
        return public_key.encrypt(
            data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def decrypt_with_private_key(self, encrypted_data: bytes, address: str) -> bytes:
        """Decrypt data with private key for an address"""
        private_key = self.get_private_key(address)
        if not private_key:
            raise ValueError(f"No private key found for address: {address}")
        
        return private_key.decrypt(
            encrypted_data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
