"""
IPFS service for handling distributed file storage
"""
import os
import hashlib
from typing import Optional, Dict, Any

from backend.ipfs_toolkit_wrapper import IPFSToolkitClient
from backend.config import LOCAL_STORAGE_PATH

class IPFSService:
    """Service for IPFS operations with local storage fallback"""

    def __init__(self):
        self.client = IPFSToolkitClient()
        self.connected = self.client.connected

    def is_connected(self) -> bool:
        """Check if IPFS is connected"""
        return self.connected

    def add_data(self, data: bytes, pin: bool = True) -> str:
        """
        Add data to IPFS with fallback to local storage

        Args:
            data: The data to store
            pin: Whether to pin the data

        Returns:
            str: The CID or local hash
        """
        try:
            if self.connected:
                # Try IPFS first
                result = self.client.add(data, pin=pin)
                if isinstance(result, dict) and "Hash" in result:
                    cid = result["Hash"]
                    print(f"✅ Stored {len(data)} bytes on IPFS: {cid}")
                    return cid
                elif isinstance(result, str):
                    print(f"✅ Stored {len(data)} bytes on IPFS: {result}")
                    return result

            # Fallback to local storage
            return self._store_locally(data)

        except Exception as e:
            print(f"❌ IPFS storage failed: {str(e)}")
            return self._store_locally(data)

    def get_data(self, cid: str) -> Optional[bytes]:
        """
        Retrieve data from IPFS with fallback to local storage

        Args:
            cid: The content identifier

        Returns:
            bytes: The retrieved data or None if not found
        """
        try:
            if self.connected:
                # Try IPFS first
                try:
                    data = self.client.cat(cid)
                    print(f"✅ Retrieved {len(data)} bytes from IPFS: {cid}")
                    return data
                except Exception as ipfs_error:
                    print(f"⚠️ IPFS retrieval failed: {str(ipfs_error)}")

            # Fallback to local storage
            return self._retrieve_locally(cid)

        except Exception as e:
            print(f"❌ Data retrieval failed: {str(e)}")
            return None

    def pin_data(self, cid: str) -> bool:
        """
        Pin data in IPFS

        Args:
            cid: The content identifier to pin

        Returns:
            bool: True if successful
        """
        try:
            if self.connected:
                result = self.client.pin_add(cid)
                print(f"✅ Pinned data: {cid}")
                return True
        except Exception as e:
            print(f"❌ Pin operation failed: {str(e)}")

        return False

    def list_pins(self) -> Dict[str, Any]:
        """
        List pinned content

        Returns:
            dict: Pinned content information
        """
        try:
            if self.connected:
                return self.client.pin_ls()
        except Exception as e:
            print(f"❌ List pins failed: {str(e)}")

        return {"Keys": {}}

    def verify_content(self, cid: str) -> Dict[str, Any]:
        """
        Verify if content exists in IPFS or local storage

        Args:
            cid: The content identifier to verify

        Returns:
            dict: Verification results
        """
        result = {
            "cid": cid,
            "exists_ipfs": False,
            "exists_local": False,
            "size_bytes": None,
            "verified": False
        }

        # Check IPFS
        if self.connected:
            try:
                data = self.client.cat(cid)
                if data:
                    result["exists_ipfs"] = True
                    result["size_bytes"] = len(data)
                    result["verified"] = True
            except Exception:
                pass

        # Check local storage
        local_path = os.path.join(LOCAL_STORAGE_PATH, cid)
        if os.path.exists(local_path):
            result["exists_local"] = True
            if not result["size_bytes"]:
                result["size_bytes"] = os.path.getsize(local_path)
            result["verified"] = True

        return result

    def _store_locally(self, data: bytes) -> str:
        """Store data locally and return a hash as CID"""
        # Generate a hash as the local "CID"
        local_cid = hashlib.sha256(data).hexdigest()

        # Ensure local storage directory exists
        os.makedirs(LOCAL_STORAGE_PATH, exist_ok=True)

        # Store the data
        local_path = os.path.join(LOCAL_STORAGE_PATH, local_cid)
        with open(local_path, "wb") as f:
            f.write(data)

        print(f"✅ Stored {len(data)} bytes locally: {local_cid}")
        return local_cid

    def _retrieve_locally(self, cid: str) -> Optional[bytes]:
        """Retrieve data from local storage"""
        local_path = os.path.join(LOCAL_STORAGE_PATH, cid)

        if os.path.exists(local_path):
            with open(local_path, "rb") as f:
                data = f.read()
            print(f"✅ Retrieved {len(data)} bytes from local storage: {cid}")
            return data

        print(f"❌ Data not found locally: {cid}")
        return None
