"""
Record-related Pydantic models
"""
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

class RecordData(BaseModel):
    """Model for medical record data"""
    patientId: str
    patientName: str
    age: int
    diagnosis: str
    treatment: str
    doctorId: str
    hospitalId: str
    timestamp: Optional[str] = None

class RecordSign(BaseModel):
    """Model for record signing request"""
    record_data: Dict[str, Any]
    wallet_address: str

class RecordStore(BaseModel):
    """Model for record storage request"""
    record: Dict[str, Any]
    signature: str
    merkleRoot: str
    patientAddress: str
    hospitalInfo: Optional[str] = "General Hospital"

class RecordRetrieve(BaseModel):
    """Model for record retrieval request"""
    cid: str
    signature: str
    eId: str
    patientAddress: str

class RecordList(BaseModel):
    """Model for listing patient records"""
    patient_address: str

class RecordShare(BaseModel):
    """Model for record sharing request"""
    record_cid: str
    doctor_address: str
    wallet_address: str

class RecordResponse(BaseModel):
    """Model for record operation response"""
    cid: Optional[str] = None
    merkleRoot: Optional[str] = None
    eId: Optional[str] = None
    txHash: Optional[str] = None
    gasUsed: Optional[int] = None
    gasPrice: Optional[int] = None
    gasPriceGwei: Optional[float] = None
    simulated: Optional[bool] = False
