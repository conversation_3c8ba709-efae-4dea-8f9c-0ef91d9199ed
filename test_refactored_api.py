#!/usr/bin/env python3
"""
Test script for the refactored healthcare data sharing API
"""
import os
import sys
import time
import requests
import json

# Set IPFS_PATH for CLI usage
os.environ['IPFS_PATH'] = os.path.expanduser('~/.ipfs')

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """Test that all modules can be imported"""
    print("🧪 Testing module imports...")
    
    try:
        # Test config
        from backend.config import success_response, PATIENT_ADDRESS
        print("✅ Config module imported successfully")
        
        # Test models
        from backend.models.auth import AuthChallenge
        from backend.models.records import RecordData
        from backend.models.sharing import ShareRequest
        print("✅ Models imported successfully")
        
        # Test services
        from backend.services.key_manager import KeyManager
        from backend.services.ipfs_service import IPFSService
        print("✅ Services imported successfully")
        
        # Test routers
        from backend.routers import auth, records, sharing, health
        print("✅ Routers imported successfully")
        
        # Test main app
        from backend.main import create_app
        print("✅ Main app imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        return False

def test_services():
    """Test that services can be initialized"""
    print("\n🧪 Testing service initialization...")
    
    try:
        # Test KeyManager
        from backend.services.key_manager import KeyManager
        key_manager = KeyManager()
        print(f"✅ KeyManager initialized with {len(key_manager.key_store)} keys")
        
        # Test IPFSService
        from backend.services.ipfs_service import IPFSService
        ipfs_service = IPFSService()
        print(f"✅ IPFSService initialized (Connected: {ipfs_service.is_connected()})")
        
        return True
        
    except Exception as e:
        print(f"❌ Service initialization failed: {str(e)}")
        return False

def test_app_creation():
    """Test that the FastAPI app can be created"""
    print("\n🧪 Testing FastAPI app creation...")
    
    try:
        from backend.main import create_app
        app = create_app()
        print(f"✅ FastAPI app created: {app.title}")
        print(f"✅ Routes registered: {len(app.routes)}")
        
        # List some routes
        route_paths = [route.path for route in app.routes if hasattr(route, 'path')]
        print(f"✅ Sample routes: {route_paths[:5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ App creation failed: {str(e)}")
        return False

def test_key_operations():
    """Test key management operations"""
    print("\n🧪 Testing key operations...")
    
    try:
        from backend.services.key_manager import KeyManager
        from backend.config import PATIENT_ADDRESS, DOCTOR_ADDRESS
        
        key_manager = KeyManager()
        
        # Test key retrieval
        patient_private_key = key_manager.get_private_key(PATIENT_ADDRESS)
        patient_public_key = key_manager.get_public_key(PATIENT_ADDRESS)
        
        if patient_private_key and patient_public_key:
            print("✅ Patient keys retrieved successfully")
        else:
            print("❌ Failed to retrieve patient keys")
            return False
        
        # Test encryption/decryption
        test_data = b"Hello, World!"
        encrypted_data = key_manager.encrypt_with_public_key(test_data, PATIENT_ADDRESS)
        decrypted_data = key_manager.decrypt_with_private_key(encrypted_data, PATIENT_ADDRESS)
        
        if decrypted_data == test_data:
            print("✅ Encryption/decryption test passed")
        else:
            print("❌ Encryption/decryption test failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Key operations failed: {str(e)}")
        return False

def test_ipfs_operations():
    """Test IPFS operations"""
    print("\n🧪 Testing IPFS operations...")
    
    try:
        from backend.services.ipfs_service import IPFSService
        
        ipfs_service = IPFSService()
        
        # Test data storage and retrieval
        test_data = b"Test data for IPFS"
        cid = ipfs_service.add_data(test_data)
        print(f"✅ Data stored with CID: {cid}")
        
        retrieved_data = ipfs_service.get_data(cid)
        if retrieved_data == test_data:
            print("✅ Data retrieval test passed")
        else:
            print("❌ Data retrieval test failed")
            return False
        
        # Test content verification
        verification_result = ipfs_service.verify_content(cid)
        if verification_result["verified"]:
            print("✅ Content verification test passed")
        else:
            print("❌ Content verification test failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ IPFS operations failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Refactored Healthcare Data Sharing API")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Service Initialization", test_services),
        ("App Creation", test_app_creation),
        ("Key Operations", test_key_operations),
        ("IPFS Operations", test_ipfs_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactored API is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
