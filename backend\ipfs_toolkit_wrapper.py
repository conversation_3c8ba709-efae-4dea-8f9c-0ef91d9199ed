"""
IPFS Toolkit Wrapper - Modern IPFS client using ipfs-toolkit and CLI
This module provides a clean interface to IPFS using the ipfs-toolkit library
and IPFS CLI as fallbacks, replacing ipfshttpclient.
"""

import os
import json
import tempfile
import hashlib
import subprocess
from typing import Dict, List, Optional, Union, BinaryIO

try:
    import ipfs_api
    IPFS_TOOLKIT_AVAILABLE = True
except ImportError:
    print("Warning: ipfs-toolkit not available. Falling back to CLI.")
    IPFS_TOOLKIT_AVAILABLE = False
    ipfs_api = None

# Check if IPFS CLI is available
def check_ipfs_cli():
    """Check if IPFS CLI is available"""
    try:
        result = subprocess.run(['ipfs', 'version'], capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

IPFS_CLI_AVAILABLE = check_ipfs_cli()

# Import requests for HTTP API fallback
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    print("Warning: requests package not found. HTTP API fallback will not work.")
    REQUESTS_AVAILABLE = False
    requests = None


class IPFSToolkitClient:
    """
    IPFS client using ipfs-toolkit with fallback to CLI and HTTP API
    """

    def __init__(self, api_url: str = None):
        """
        Initialize IPFS client

        Args:
            api_url: IPFS API URL (e.g., "http://127.0.0.1:5001") - used for HTTP fallback
        """
        self.api_url = api_url or os.environ.get("IPFS_URL", "http://127.0.0.1:5001")
        self.api_host = os.environ.get("IPFS_API_HOST", "127.0.0.1")
        self.api_port = os.environ.get("IPFS_API_PORT", "5001")
        self.connected = False
        self.use_cli = IPFS_CLI_AVAILABLE

        # Try to connect
        self._test_connection()

    def _test_connection(self) -> bool:
        """Test IPFS connection"""
        try:
            # First try CLI
            if IPFS_CLI_AVAILABLE:
                try:
                    result = subprocess.run(['ipfs', 'id'], capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        data = json.loads(result.stdout)
                        peer_id = data.get('ID', 'unknown')
                        print(f"✅ Connected to IPFS using CLI. Peer ID: {peer_id}")
                        self.connected = True
                        self.use_cli = True
                        return True
                except (subprocess.TimeoutExpired, json.JSONDecodeError, Exception) as e:
                    print(f"⚠️ IPFS CLI test failed: {str(e)}")

            # Fallback to ipfs-toolkit
            if IPFS_TOOLKIT_AVAILABLE:
                try:
                    peer_id = ipfs_api.my_id()
                    if peer_id:
                        print(f"✅ Connected to IPFS using ipfs-toolkit. Peer ID: {peer_id}")
                        self.connected = True
                        self.use_cli = False
                        return True
                except Exception as e:
                    print(f"⚠️ ipfs-toolkit test failed: {str(e)}")

            # Fallback to HTTP API test
            if REQUESTS_AVAILABLE:
                try:
                    response = requests.post(f"{self.api_url}/api/v0/id", timeout=5)
                    if response.status_code == 200:
                        data = response.json()
                        print(f"✅ Connected to IPFS using HTTP API. Peer ID: {data.get('ID', 'unknown')}")
                        self.connected = True
                        self.use_cli = False
                        return True
                except Exception as e:
                    print(f"⚠️ HTTP API test failed: {str(e)}")

            print("❌ Could not connect to IPFS using any method")
            self.connected = False
            return False

        except Exception as e:
            print(f"❌ IPFS connection test failed: {str(e)}")
            self.connected = False
            return False

    def id(self) -> Dict:
        """Get IPFS node ID and info"""
        try:
            # Try CLI first
            if self.use_cli and IPFS_CLI_AVAILABLE:
                result = subprocess.run(['ipfs', 'id'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return json.loads(result.stdout)

            # Fallback to ipfs-toolkit
            if IPFS_TOOLKIT_AVAILABLE and self.connected:
                peer_id = ipfs_api.my_id()
                return {"ID": peer_id}

            # Fallback to HTTP API
            if REQUESTS_AVAILABLE:
                response = requests.post(f"{self.api_url}/api/v0/id")
                if response.status_code == 200:
                    return response.json()

            raise Exception("No IPFS connection available")

        except Exception as e:
            raise Exception(f"Failed to get IPFS node ID: {str(e)}")

    def add(self, data: Union[str, bytes, BinaryIO], **kwargs) -> Dict:
        """
        Add data to IPFS

        Args:
            data: Data to add (file path, bytes, or file-like object)
            **kwargs: Additional options (pin, cid_version, etc.)

        Returns:
            Dict with 'Hash' (CID) and other metadata
        """
        try:
            # Handle different data types
            if isinstance(data, str):
                # File path
                if os.path.isfile(data):
                    return self._add_file(data, **kwargs)
                else:
                    # String content
                    return self._add_bytes(data.encode('utf-8'), **kwargs)
            elif isinstance(data, bytes):
                return self._add_bytes(data, **kwargs)
            elif hasattr(data, 'read'):
                # File-like object
                content = data.read()
                if isinstance(content, str):
                    content = content.encode('utf-8')
                return self._add_bytes(content, **kwargs)
            else:
                raise ValueError(f"Unsupported data type: {type(data)}")

        except Exception as e:
            raise Exception(f"Failed to add data to IPFS: {str(e)}")

    def _add_file(self, file_path: str, **kwargs) -> Dict:
        """Add a file to IPFS"""
        try:
            # Try CLI first
            if self.use_cli and IPFS_CLI_AVAILABLE:
                pin = kwargs.get('pin', True)
                cid_version = kwargs.get('cid_version', 1)

                cmd = ['ipfs', 'add', '--cid-version', str(cid_version)]
                if not pin:
                    cmd.append('--pin=false')
                cmd.append(file_path)

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    # Parse output: "added <hash> <filename>"
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if line.startswith('added '):
                            parts = line.split(' ', 2)
                            if len(parts) >= 2:
                                return {"Hash": parts[1], "Name": os.path.basename(file_path)}
                    raise Exception("Could not parse IPFS CLI output")
                else:
                    raise Exception(f"IPFS CLI error: {result.stderr}")

            # Fallback to ipfs-toolkit
            if IPFS_TOOLKIT_AVAILABLE and self.connected:
                cid = ipfs_api.publish(file_path)
                return {"Hash": cid, "Name": os.path.basename(file_path)}

            # Fallback to HTTP API
            if REQUESTS_AVAILABLE:
                pin = kwargs.get('pin', True)
                cid_version = kwargs.get('cid_version', 1)

                url = f"{self.api_url}/api/v0/add"
                params = {
                    'pin': 'true' if pin else 'false',
                    'cid-version': cid_version
                }

                with open(file_path, 'rb') as f:
                    files = {'file': f}
                    response = requests.post(url, files=files, params=params)

                if response.status_code == 200:
                    result = response.json()
                    return result
                else:
                    raise Exception(f"HTTP API error: {response.status_code} - {response.text}")

            raise Exception("No IPFS connection available")

        except Exception as e:
            raise Exception(f"Failed to add file to IPFS: {str(e)}")

    def _add_bytes(self, data: bytes, **kwargs) -> Dict:
        """Add bytes to IPFS"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(data)
                temp_path = temp_file.name

            try:
                result = self._add_file(temp_path, **kwargs)
                return result
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            raise Exception(f"Failed to add bytes to IPFS: {str(e)}")

    def cat(self, cid: str) -> bytes:
        """
        Retrieve data from IPFS by CID

        Args:
            cid: Content Identifier

        Returns:
            bytes: The content
        """
        try:
            # Try CLI first
            if self.use_cli and IPFS_CLI_AVAILABLE:
                result = subprocess.run(['ipfs', 'cat', cid], capture_output=True, timeout=30)
                if result.returncode == 0:
                    return result.stdout
                else:
                    raise Exception(f"IPFS CLI error: {result.stderr.decode()}")

            # ipfs-toolkit doesn't have direct cat, skip to HTTP API

            # Use HTTP API
            if REQUESTS_AVAILABLE:
                response = requests.post(f"{self.api_url}/api/v0/cat", params={'arg': cid})
                if response.status_code == 200:
                    return response.content
                else:
                    raise Exception(f"HTTP API error: {response.status_code} - {response.text}")

            raise Exception("No IPFS connection available")

        except Exception as e:
            raise Exception(f"Failed to retrieve data from IPFS: {str(e)}")

    def pin_add(self, cid: str) -> Dict:
        """Pin content to prevent garbage collection"""
        try:
            # Try CLI first
            if self.use_cli and IPFS_CLI_AVAILABLE:
                result = subprocess.run(['ipfs', 'pin', 'add', cid], capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    return {"Pins": [cid]}
                else:
                    raise Exception(f"IPFS CLI error: {result.stderr}")

            # Fallback to HTTP API
            if REQUESTS_AVAILABLE:
                response = requests.post(f"{self.api_url}/api/v0/pin/add", params={'arg': cid})
                if response.status_code == 200:
                    return {"Pins": [cid]}
                else:
                    raise Exception(f"HTTP API error: {response.status_code} - {response.text}")

            raise Exception("No IPFS connection available")

        except Exception as e:
            raise Exception(f"Failed to pin content: {str(e)}")

    def pin_ls(self, **kwargs) -> Dict:
        """List pinned content"""
        try:
            # Try CLI first
            if self.use_cli and IPFS_CLI_AVAILABLE:
                result = subprocess.run(['ipfs', 'pin', 'ls', '--type=recursive'], capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    pins = {}
                    for line in result.stdout.strip().split('\n'):
                        if line:
                            parts = line.split(' ', 1)
                            if len(parts) >= 1:
                                cid = parts[0]
                                pins[cid] = {"Type": "recursive"}
                    return {"Keys": pins}
                else:
                    print(f"Warning: IPFS CLI pin ls failed: {result.stderr}")

            # Fallback to HTTP API
            if REQUESTS_AVAILABLE:
                response = requests.post(f"{self.api_url}/api/v0/pin/ls")
                if response.status_code == 200:
                    # Parse the response (it's newline-delimited JSON)
                    pins = {}
                    for line in response.text.strip().split('\n'):
                        if line:
                            pin_info = json.loads(line)
                            pins[pin_info['Cid']] = pin_info
                    return {"Keys": pins}
                else:
                    print(f"Warning: HTTP API pin ls failed: {response.status_code} - {response.text}")

            print("Warning: No IPFS connection available for pin ls")
            return {"Keys": {}}

        except Exception as e:
            print(f"Warning: Failed to list pins: {str(e)}")
            return {"Keys": {}}

    def is_connected(self) -> bool:
        """Check if IPFS is connected"""
        return self.connected


# Global client instance
_ipfs_client = None

def get_ipfs_client(api_url: str = None) -> IPFSToolkitClient:
    """Get or create IPFS client instance"""
    global _ipfs_client
    if _ipfs_client is None:
        _ipfs_client = IPFSToolkitClient(api_url)
    return _ipfs_client

def connect(api_url: str = None) -> IPFSToolkitClient:
    """Connect to IPFS (compatibility function)"""
    return get_ipfs_client(api_url)
