#!/bin/bash

# Healthcare Data Sharing Streamlit App Startup Script
# This script starts the Streamlit frontend connected to the refactored API

echo "🚀 Starting Healthcare Data Sharing Streamlit App"
echo "================================================="

# Set environment variables
export IPFS_PATH=$HOME/.ipfs
export MCL_LIB_PATH=$PWD/mcl/build/lib

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Virtual environment not detected. Activating..."
    source venv/bin/activate
fi

# Check if the API is running
echo "🔍 Checking if API is running..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ API is running at http://localhost:8000"
else
    echo "❌ API is not running. Please start the API first:"
    echo "   ./run_refactored_api.sh"
    echo ""
    echo "🔄 Continuing anyway - you can start the API later..."
fi

# Check if required dependencies are installed
echo "🔍 Checking dependencies..."
python -c "import streamlit, requests" 2>/dev/null || {
    echo "❌ Missing dependencies. Installing..."
    pip install streamlit requests
}

# Set API URL for the Streamlit app
export API_URL="http://127.0.0.1:8000"

# Start the Streamlit app
echo "🚀 Starting Streamlit app..."
echo "📡 App will be available at: http://localhost:8501"
echo "🔗 Connected to API at: $API_URL"
echo "🔄 Press Ctrl+C to stop the app"
echo ""

# Change to app directory and run Streamlit
cd app
streamlit run main.py --server.port 8501 --server.address 0.0.0.0

echo "🔄 Streamlit app stopped."
