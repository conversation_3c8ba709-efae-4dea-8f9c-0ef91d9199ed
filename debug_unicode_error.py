#!/usr/bin/env python3
"""
Debug script to identify the exact source of the Unicode decode error
"""

import sys
import os
import json
import hashlib
import time

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_merkle_service():
    """Test MerkleService to see if it returns binary data"""
    print("🧪 Testing MerkleService for binary data")
    print("=" * 60)
    
    try:
        from backend.data import MerkleService
        
        # Test data similar to what would be used in the API
        test_record = {
            "patientId": "0xEDB64f85F1fC9357EcA100C2970f7F84a5faAD4A",
            "date": "2023-05-01",
            "diagnosis": "Hypertension",
            "notes": "Patient should monitor blood pressure daily",
            "doctorId": "0x1f67F0a4B2416bb3aE42A06bDfDc227848998716"
        }
        
        merkle_service = MerkleService()
        merkle_root, proofs = merkle_service.create_merkle_tree(test_record)
        
        print(f"✅ Merkle root: {merkle_root}")
        print(f"   Type: {type(merkle_root)}")
        print(f"   Length: {len(merkle_root) if merkle_root else 0}")
        
        print(f"✅ Proofs: {type(proofs)}")
        print(f"   Keys: {list(proofs.keys()) if isinstance(proofs, dict) else 'Not a dict'}")
        
        # Check each proof for binary data
        for key, proof in proofs.items():
            print(f"   Proof for '{key}': {type(proof)}")
            if isinstance(proof, list):
                for i, item in enumerate(proof):
                    print(f"     Item {i}: {type(item)} - {str(item)[:100]}")
                    if isinstance(item, dict):
                        for k, v in item.items():
                            print(f"       {k}: {type(v)} - {str(v)[:50]}")
                            if isinstance(v, bytes):
                                print(f"         ❌ FOUND BINARY DATA in proof!")
                                return False
        
        # Test JSON serialization
        try:
            test_data = {
                "merkleRoot": merkle_root,
                "proofs": proofs
            }
            json_string = json.dumps(test_data)
            print(f"✅ JSON serialization successful ({len(json_string)} chars)")
            return True
        except Exception as json_error:
            print(f"❌ JSON serialization failed: {str(json_error)}")
            return False
            
    except Exception as e:
        print(f"❌ MerkleService test failed: {str(e)}")
        return False

def test_group_signature():
    """Test group signature to see if it returns binary data"""
    print("\n🧪 Testing Group Signature for binary data")
    print("=" * 60)
    
    try:
        from backend.groupsig_utils import sign_message
        
        # Test signing a message
        test_message = "test_merkle_root_hash"
        signature = sign_message(test_message)
        
        print(f"✅ Signature: {type(signature)}")
        print(f"   Value: {str(signature)[:100]}...")
        
        if isinstance(signature, bytes):
            print(f"   ❌ FOUND BINARY SIGNATURE DATA!")
            return False
        
        # Test JSON serialization
        try:
            test_data = {"signature": signature}
            json_string = json.dumps(test_data)
            print(f"✅ JSON serialization successful ({len(json_string)} chars)")
            return True
        except Exception as json_error:
            print(f"❌ JSON serialization failed: {str(json_error)}")
            return False
            
    except Exception as e:
        print(f"❌ Group signature test failed: {str(e)}")
        return False

def test_sanitize_function():
    """Test the sanitize function with various data types"""
    print("\n🧪 Testing _sanitize_for_json function")
    print("=" * 60)
    
    try:
        # Import the sanitize function
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'routers'))
        from records import _sanitize_for_json
        
        # Test cases with binary data
        test_cases = [
            {"name": "bytes", "data": b'\x99\x88\x77\x66'},
            {"name": "string", "data": "normal string"},
            {"name": "dict_with_bytes", "data": {"key": b'\x99\x88\x77\x66', "normal": "value"}},
            {"name": "list_with_bytes", "data": [b'\x99\x88\x77\x66', "normal", 123]},
            {"name": "nested", "data": {"level1": {"level2": [b'\x99\x88\x77\x66', "text"]}}},
        ]
        
        for test_case in test_cases:
            print(f"\n🔍 Testing {test_case['name']}")
            try:
                sanitized = _sanitize_for_json(test_case['data'])
                print(f"   Original: {type(test_case['data'])} - {str(test_case['data'])[:50]}")
                print(f"   Sanitized: {type(sanitized)} - {str(sanitized)[:50]}")
                
                # Test JSON serialization
                json_string = json.dumps(sanitized)
                print(f"   ✅ JSON serialization successful ({len(json_string)} chars)")
                
            except Exception as e:
                print(f"   ❌ Sanitization failed: {str(e)}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Sanitize function test failed: {str(e)}")
        return False

def test_full_workflow():
    """Test the full workflow that might cause the Unicode error"""
    print("\n🧪 Testing Full Store Record Workflow")
    print("=" * 60)
    
    try:
        from backend.data import MerkleService, encrypt_record
        from backend.groupsig_utils import sign_message
        
        # Simulate the store record workflow
        record = {
            "patientId": "0xEDB64f85F1fC9357EcA100C2970f7F84a5faAD4A",
            "date": "2023-05-01",
            "diagnosis": "Hypertension"
        }
        
        signature = "mock_signature_12345"
        merkle_root = "mock_merkle_root_67890"
        patient_address = "0xEDB64f85F1fC9357EcA100C2970f7F84a5faAD4A"
        hospital_info = "General Hospital"
        
        print(f"📝 Testing workflow components...")
        
        # Test patient key generation
        patient_key = hashlib.sha256(f"{patient_address}_key".encode()).digest()
        print(f"   Patient key: {type(patient_key)} - {len(patient_key)} bytes")
        
        # Test record encryption
        encrypted_record = encrypt_record(record, patient_key)
        print(f"   Encrypted record: {type(encrypted_record)} - {len(encrypted_record)} bytes")
        
        # Test eId generation
        eId = f"mock_eid_{hashlib.sha256(f'{hospital_info}_{patient_key.hex()}_{int(time.time())}'.encode()).hexdigest()}"
        print(f"   eId: {type(eId)} - {eId[:50]}...")
        
        # Test response data structure
        response_data = {
            "cid": "mock_cid_12345",
            "merkleRoot": merkle_root,
            "eId": eId,
            "txHash": "0x1234567890abcdef",
            "gasUsed": 200000,
            "gasPrice": 10000000000,
            "gasPriceGwei": 10.0,
            "simulated": True
        }
        
        print(f"   Response data keys: {list(response_data.keys())}")
        
        # Test JSON serialization
        try:
            json_string = json.dumps(response_data)
            print(f"   ✅ JSON serialization successful ({len(json_string)} chars)")
            return True
        except Exception as json_error:
            print(f"   ❌ JSON serialization failed: {str(json_error)}")
            
            # Check each field individually
            for key, value in response_data.items():
                try:
                    json.dumps({key: value})
                    print(f"     ✅ {key}: OK")
                except Exception as field_error:
                    print(f"     ❌ {key}: {str(field_error)} - Type: {type(value)}")
            
            return False
            
    except Exception as e:
        print(f"❌ Full workflow test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Unicode Decode Error Debug Tests")
    print("=" * 60)
    
    # Run tests
    merkle_passed = test_merkle_service()
    signature_passed = test_group_signature()
    sanitize_passed = test_sanitize_function()
    workflow_passed = test_full_workflow()
    
    print("\n" + "=" * 60)
    print("📊 DEBUG RESULTS")
    print("=" * 60)
    
    results = {
        "Merkle Service": merkle_passed,
        "Group Signature": signature_passed,
        "Sanitize Function": sanitize_passed,
        "Full Workflow": workflow_passed
    }
    
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    if all(results.values()):
        print("\n🎉 ALL TESTS PASSED! The Unicode error might be elsewhere.")
    else:
        print("\n❌ SOME TESTS FAILED! Found potential sources of Unicode errors.")
        
    print("\n💡 Next steps:")
    print("   1. Check the exact error location in the FastAPI logs")
    print("   2. Add more debugging to the store_record endpoint")
    print("   3. Test with actual frontend data")
