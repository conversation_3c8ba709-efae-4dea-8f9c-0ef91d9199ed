"""
Authentication-related Pydantic models
"""
from pydantic import BaseModel
from typing import Optional

class AuthChallenge(BaseModel):
    """Model for authentication challenge request"""
    wallet_address: str

class AuthVerification(BaseModel):
    """Model for authentication verification request"""
    wallet_address: str
    signature: str

class AuthStatus(BaseModel):
    """Model for authentication status response"""
    authenticated: bool
    wallet_address: str
    role: Optional[str] = None

class LogoutRequest(BaseModel):
    """Model for logout request"""
    wallet_address: str
