#!/usr/bin/env python3
"""
Simple test script to check the refactored API endpoints
"""
import requests
import json

def test_api():
    """Test basic API functionality"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Refactored API")
    print("=" * 50)
    
    # Test 1: Health check
    print("\n🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print(f"✅ Health check passed: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.text}")
    except Exception as e:
        print(f"❌ Health check error: {e}")
    
    # Test 2: Records list endpoint
    print("\n🔍 Testing records list endpoint...")
    try:
        patient_address = "******************************************"
        response = requests.get(f"{base_url}/records/list", params={"patient_address": patient_address})
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Records list passed: {data}")
        else:
            print(f"❌ Records list failed: {response.text}")
            print(f"Response headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ Records list error: {e}")
    
    # Test 3: Auth challenge endpoint
    print("\n🔍 Testing auth challenge endpoint...")
    try:
        response = requests.post(f"{base_url}/auth/challenge", 
                               json={"wallet_address": "******************************************"})
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Auth challenge passed: {data}")
        else:
            print(f"❌ Auth challenge failed: {response.text}")
    except Exception as e:
        print(f"❌ Auth challenge error: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 API test completed")

if __name__ == "__main__":
    test_api()
