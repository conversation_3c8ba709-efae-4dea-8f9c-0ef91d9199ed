#!/bin/bash

# Healthcare Data Sharing API - Refactored Version Startup Script
# This script starts the new modular FastAPI application

echo "🚀 Starting Healthcare Data Sharing API (Refactored)"
echo "=================================================="

# Set environment variables
export IPFS_PATH=$HOME/.ipfs
export MCL_LIB_PATH=$PWD/mcl/build/lib

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Virtual environment not detected. Activating..."
    source venv/bin/activate
fi

# Check if IPFS is running
echo "🔍 Checking IPFS status..."
if ! ipfs id > /dev/null 2>&1; then
    echo "❌ IPFS not running. Please start IPFS first:"
    echo "   export IPFS_PATH=\$HOME/.ipfs"
    echo "   ipfs daemon"
    echo ""
    echo "🔄 Continuing with local storage fallback..."
fi

# Check if required dependencies are installed
echo "🔍 Checking dependencies..."
python -c "import fastapi, uvicorn" 2>/dev/null || {
    echo "❌ Missing dependencies. Installing..."
    pip install fastapi uvicorn
}

# Start the API server
echo "🚀 Starting FastAPI server..."
echo "📡 API will be available at: http://localhost:8000"
echo "📚 API documentation at: http://localhost:8000/docs"
echo "🔄 Press Ctrl+C to stop the server"
echo ""

# Run the new modular API
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8000 --reload

echo "🔄 API server stopped."
