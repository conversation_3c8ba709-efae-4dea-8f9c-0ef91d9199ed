"""
Verification-related Pydantic models
"""
from pydantic import BaseModel
from typing import Optional

class VerificationRequest(BaseModel):
    """Model for verification request"""
    cid: str

class VerificationResponse(BaseModel):
    """Model for verification response"""
    cid: str
    exists_ipfs: bool
    exists_local: bool
    size_bytes: Optional[int] = None
    verified: bool

class OpeningRequest(BaseModel):
    """Model for signature opening request"""
    opening_id: int
    signature: str
    manager_type: str  # "group" or "revocation"
    wallet_address: str

class OpeningResponse(BaseModel):
    """Model for signature opening response"""
    opening_id: int
    success: bool
    result: Optional[str] = None
    message: Optional[str] = None
