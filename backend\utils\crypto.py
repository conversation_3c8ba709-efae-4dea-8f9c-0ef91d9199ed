"""
Cryptographic utility functions
"""
import base64
from typing import Union
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend

def encrypt_with_public_key(data: bytes, public_key: rsa.RSAPublicKey) -> bytes:
    """
    Encrypt data with RSA public key using OAEP padding
    
    Args:
        data: Data to encrypt
        public_key: RSA public key
        
    Returns:
        bytes: Encrypted data
    """
    return public_key.encrypt(
        data,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )

def decrypt_with_private_key(encrypted_data: bytes, private_key: rsa.RSAPrivateKey) -> bytes:
    """
    Decrypt data with RSA private key using OAEP padding
    
    Args:
        encrypted_data: Encrypted data
        private_key: RSA private key
        
    Returns:
        bytes: Decrypted data
    """
    return private_key.decrypt(
        encrypted_data,
        padding.OAEP(
            mgf=padding.MGF1(algorithm=hashes.SHA256()),
            algorithm=hashes.SHA256(),
            label=None
        )
    )

def serialize_public_key(public_key: rsa.RSAPublicKey) -> str:
    """
    Serialize public key to PEM format string
    
    Args:
        public_key: RSA public key
        
    Returns:
        str: PEM formatted public key
    """
    pem_bytes = public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    )
    return pem_bytes.decode('utf-8')

def deserialize_public_key(pem_string: str) -> rsa.RSAPublicKey:
    """
    Deserialize public key from PEM format string
    
    Args:
        pem_string: PEM formatted public key
        
    Returns:
        rsa.RSAPublicKey: RSA public key
    """
    return serialization.load_pem_public_key(
        pem_string.encode('utf-8'),
        backend=default_backend()
    )

def serialize_private_key(private_key: rsa.RSAPrivateKey) -> str:
    """
    Serialize private key to PEM format string
    
    Args:
        private_key: RSA private key
        
    Returns:
        str: PEM formatted private key
    """
    pem_bytes = private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    )
    return pem_bytes.decode('utf-8')

def deserialize_private_key(pem_string: str) -> rsa.RSAPrivateKey:
    """
    Deserialize private key from PEM format string
    
    Args:
        pem_string: PEM formatted private key
        
    Returns:
        rsa.RSAPrivateKey: RSA private key
    """
    return serialization.load_pem_private_key(
        pem_string.encode('utf-8'),
        password=None,
        backend=default_backend()
    )

def encode_base64(data: bytes) -> str:
    """
    Encode bytes to base64 string
    
    Args:
        data: Data to encode
        
    Returns:
        str: Base64 encoded string
    """
    return base64.b64encode(data).decode('utf-8')

def decode_base64(encoded_string: str) -> bytes:
    """
    Decode base64 string to bytes
    
    Args:
        encoded_string: Base64 encoded string
        
    Returns:
        bytes: Decoded data
    """
    return base64.b64decode(encoded_string.encode('utf-8'))
