"""
Buyer-specific endpoints
"""
import os
import json
import time
from fastapi import APIRouter, Query
from typing import Optional

from backend.config import success_response, error_response, LOCAL_STORAGE_PATH, BUYER_ADDRESS

router = APIRouter()

@router.get("/buyer/filled-templates")
async def get_filled_templates(wallet_address: str):
    """
    Get filled templates for a buyer
    """
    try:
        print(f"Getting filled templates for buyer {wallet_address}")

        # For demo purposes, we'll look for purchase requests that have been filled by patients
        # In a real implementation, we would filter requests based on the buyer's address

        # Check if the local storage directory exists
        purchases_dir = os.path.join(LOCAL_STORAGE_PATH, "purchases")
        if not os.path.exists(purchases_dir):
            return success_response(data={"templates": []})

        # Get all purchase files
        purchase_files = os.listdir(purchases_dir)

        # Filter for requests that have been filled by patients
        templates = []
        for file_name in purchase_files:
            if not file_name.endswith(".json"):
                continue

            file_path = os.path.join(purchases_dir, file_name)

            try:
                with open(file_path, "r") as f:
                    purchase_data = json.load(f)

                # Check if this request has been filled by a patient and belongs to this buyer
                if purchase_data.get("status") == "completed" and purchase_data.get("buyer_address") == wallet_address:
                    # Check if the request has multiple templates
                    if "templates" in purchase_data and purchase_data["templates"]:
                        # Create a template object for each template in the request
                        for template_info in purchase_data["templates"]:
                            template = {
                                "request_id": purchase_data.get("request_id"),
                                "patient": purchase_data.get("patient_address", "Unknown"),
                                "hospital": purchase_data.get("hospital_address", "Unknown"),
                                "template_cid": template_info.get("template_cid"),
                                "cert_cid": template_info.get("cert_cid"),
                                "status": "filled",
                                "timestamp": template_info.get("filled_at", purchase_data.get("patient_filled_at", int(time.time()))),
                                "template": purchase_data.get("filled_template", {}),
                                "verified": template_info.get("verified", False)
                            }

                            # Add to templates list
                            templates.append(template)
                    else:
                        # Fallback for older format with a single template
                        template = {
                            "request_id": purchase_data.get("request_id"),
                            "patient": purchase_data.get("patient_address", "Unknown"),
                            "hospital": purchase_data.get("hospital_address", "Unknown"),
                            "template_cid": purchase_data.get("filled_template_cid"),
                            "cert_cid": purchase_data.get("cert_cid"),
                            "status": "filled",
                            "timestamp": purchase_data.get("patient_filled_at", int(time.time())),
                            "template": purchase_data.get("filled_template", {}),
                            "verified": purchase_data.get("verified", False)
                        }

                        # Add to templates list
                        templates.append(template)
            except Exception as e:
                print(f"Error processing file {file_name}: {str(e)}")

        # Sort by timestamp (newest first)
        templates.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

        return success_response(data={"templates": templates})
    except Exception as e:
        print(f"Error in get_filled_templates: {str(e)}")
        return error_response(str(e), 500)

@router.get("/transactions")
async def get_transactions(wallet_address: str):
    """
    Get transaction history for a specific wallet address
    """
    try:
        print(f"Getting transactions for wallet {wallet_address}")

        transactions = []

        # Load transactions from various directories
        transaction_dirs = [
            "local_storage/transactions",
            "local_storage/storing_transactions",
            "local_storage/sharing_transactions", 
            "local_storage/purchasing_transactions",
            "local_storage/other_transactions"
        ]

        for tx_dir in transaction_dirs:
            if os.path.exists(tx_dir):
                for filename in os.listdir(tx_dir):
                    if filename.endswith(".json"):
                        file_path = os.path.join(tx_dir, filename)
                        try:
                            with open(file_path, "r") as f:
                                tx_data = json.load(f)
                            
                            # Filter by wallet address if specified
                            tx_wallet = tx_data.get("wallet_address") or tx_data.get("from_address") or tx_data.get("to_address")
                            if not wallet_address or tx_wallet == wallet_address:
                                transactions.append(tx_data)
                        except Exception as e:
                            print(f"Error loading transaction file {filename}: {str(e)}")

        # Remove duplicates based on transaction ID
        unique_transactions = {}
        for tx in transactions:
            tx_id = tx.get("id") or tx.get("txHash") or tx.get("transaction_id")
            if tx_id and tx_id not in unique_transactions:
                unique_transactions[tx_id] = tx

        # Convert back to list and sort by timestamp
        transactions = list(unique_transactions.values())
        transactions.sort(key=lambda x: x.get("timestamp", 0), reverse=True)

        return success_response(data={"transactions": transactions})
    except Exception as e:
        print(f"Error in get_transactions: {str(e)}")
        return error_response(str(e), 500)
