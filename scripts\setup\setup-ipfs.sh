#!/bin/bash
# Script to properly set up and run IPFS in Docker

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  print_error "Docker is not running. Please start Docker first."
  exit 1
fi

# Stop and remove any existing IPFS container
if docker ps -a | grep -q ipfs-node; then
  print_message "Stopping and removing existing IPFS container..."
  docker stop ipfs-node > /dev/null 2>&1
  docker rm ipfs-node > /dev/null 2>&1
fi

# Create IPFS data directory if it doesn't exist
if [ ! -d "ipfs-data" ]; then
  print_message "Creating IPFS data directory..."
  mkdir -p ipfs-data
fi

# Create local storage directory if it doesn't exist
if [ ! -d "local_storage" ]; then
  print_message "Creating local storage directory..."
  mkdir -p local_storage
  mkdir -p local_storage/records
  mkdir -p local_storage/purchases
  mkdir -p local_storage/transactions
  mkdir -p local_storage/store_transactions
  mkdir -p local_storage/share_transactions
  mkdir -p local_storage/purchase_transactions
fi

# Fix permissions on directories
print_message "Setting correct permissions on directories..."
chmod -R 777 ipfs-data
chmod -R 777 local_storage

# Initialize IPFS repository
print_message "Initializing IPFS repository..."
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 init

# Configure IPFS
print_message "Configuring IPFS for external access..."
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json API.HTTPHeaders.Access-Control-Allow-Origin '["*"]'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json API.HTTPHeaders.Access-Control-Allow-Methods '["PUT", "POST", "GET"]'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json Addresses.API '"/ip4/0.0.0.0/tcp/5001"'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config --json Addresses.Gateway '"/ip4/0.0.0.0/tcp/8080"'
docker run --rm -v "$(pwd)/ipfs-data:/data/ipfs" ipfs/kubo:v0.22.0 config Datastore.StorageMax "10GB"

# Start IPFS container
print_message "Starting IPFS container..."
docker run -d \
  --name ipfs-node \
  -p 4001:4001 \
  -p 5001:5001 \
  -p 8080:8080 \
  -v "$(pwd)/ipfs-data:/data/ipfs" \
  -v "$(pwd)/local_storage:/export" \
  -e IPFS_PROFILE=server \
  -e IPFS_PATH=/data/ipfs \
  --user 1000:1000 \
  ipfs/kubo:v0.22.0 \
  daemon --migrate=true --enable-gc

# Wait for IPFS to start
print_message "Waiting for IPFS to start..."
sleep 5

# Check if IPFS is running
print_message "Checking if IPFS is running..."
if docker ps | grep -q ipfs-node; then
  print_message "IPFS container is running!"
  
  # Test IPFS API
  print_message "Testing IPFS API..."
  if curl -s -X POST "http://127.0.0.1:5001/api/v0/id" > /dev/null; then
    print_message "✅ IPFS API is accessible!"
  else
    print_warning "⚠️ IPFS API is not accessible yet. It might need more time to start."
    print_warning "Try testing it manually with: curl -X POST \"http://127.0.0.1:5001/api/v0/id\""
  fi
  
  print_message "IPFS setup complete! The container is running with the following configuration:"
  print_message "- IPFS API: http://127.0.0.1:5001"
  print_message "- IPFS Gateway: http://127.0.0.1:8080"
  print_message "- IPFS Swarm: /ip4/0.0.0.0/tcp/4001"
  print_message "- Data directory: $(pwd)/ipfs-data"
  print_message "- Export directory: $(pwd)/local_storage"
  
  print_message "You can check the logs with: docker logs ipfs-node"
else
  print_error "❌ Failed to start IPFS container. Please check the logs with: docker logs ipfs-node"
  exit 1
fi
