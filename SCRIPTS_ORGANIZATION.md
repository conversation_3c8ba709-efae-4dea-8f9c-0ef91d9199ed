# 📁 Scripts Organization Plan

## 🎯 Objective
Organize all script files into a structured `scripts/` directory with clear categorization for better maintainability and discoverability.

## 📂 Proposed Directory Structure

```
scripts/
├── setup/              # Installation and setup scripts
│   ├── setup-ipfs.sh
│   ├── init-ipfs.sh
│   ├── docker-setup.sh
│   ├── configure-firewall.sh
│   └── docker-daemon-config.sh
├── development/        # Development and runtime scripts  
│   ├── run_api.sh
│   ├── run_refactored_api.sh
│   ├── run_streamlit_app.sh
│   ├── run-simple.sh
│   ├── start.sh
│   ├── start_api.sh
│   └── run.py
├── testing/           # Testing and validation scripts
│   ├── run_tests.sh
│   ├── run_test_wsl.sh
│   ├── run_contract_test.sh
│   ├── docker_test_contract.sh
│   ├── test_mcl_in_container.sh
│   └── generate_test_records.py
├── deployment/        # Deployment and contract scripts
│   ├── deploy.js
│   ├── compile_contract.sh
│   └── check-balance.js
├── maintenance/       # Maintenance and cleanup scripts
│   ├── clean-ipfs.sh
│   ├── fix-ipfs-permissions.sh
│   ├── optimize-network.sh
│   ├── stop-all-containers.sh
│   └── verify-docker-host.sh
├── blockchain/        # Blockchain-specific scripts
│   ├── check_keys.py
│   ├── fix_keys.py
│   ├── generate_cpy06_keys.py
│   ├── regenerate_keys.py
│   └── cpy06_key_gen.py
├── ipfs/             # IPFS-related scripts
│   ├── setup-ipfs.sh
│   ├── init-ipfs.sh
│   ├── clean-ipfs.sh
│   └── fix-ipfs-permissions.sh
├── docker/           # Docker and containerization scripts
│   ├── docker-setup.sh
│   ├── docker-daemon-config.sh
│   ├── stop-all-containers.sh
│   └── verify-docker-host.sh
├── utilities/        # General utility scripts
│   ├── check-api.sh
│   ├── check-external-access.sh
│   └── generate_template.py
└── README.md         # Documentation
```

## 🔄 Migration Plan

### Phase 1: Create Directory Structure
```bash
mkdir -p scripts/{setup,development,testing,deployment,maintenance,blockchain,ipfs,docker,utilities}
```

### Phase 2: Move Shell Scripts
- **Setup**: setup-ipfs.sh, init-ipfs.sh, docker-setup.sh, configure-firewall.sh
- **Development**: run_*.sh, start*.sh
- **Testing**: *test*.sh scripts from tests/scripts/
- **Deployment**: compile_contract.sh
- **Maintenance**: clean-ipfs.sh, fix-ipfs-permissions.sh, optimize-network.sh, stop-all-containers.sh
- **Utilities**: check-*.sh scripts

### Phase 3: Move Python Scripts
- **Blockchain**: *key*.py, cpy06_key_gen.py
- **Development**: run.py, run_api.py
- **Testing**: generate_test_records.py
- **Utilities**: generate_template.py

### Phase 4: Move JavaScript Scripts
- **Deployment**: deploy.js
- **Blockchain**: check-balance.js

## 📋 Current Script Inventory

### Shell Scripts (.sh)
- check-api.sh
- check-external-access.sh
- clean-ipfs.sh
- compile_contract.sh
- fix-ipfs-permissions.sh
- optimize-network.sh
- run_api.sh
- run_refactored_api.sh
- run_streamlit_app.sh
- run-simple.sh
- start.sh
- start_api.sh
- stop-all-containers.sh
- verify-docker-host.sh

### Python Scripts (.py)
- check_keys.py
- cpy06_key_gen.py
- fix_keys.py
- generate_cpy06_keys.py
- regenerate_keys.py
- run.py
- run_api.py

### JavaScript Scripts (.js)
- scripts/check-balance.js
- scripts/deploy.js
- scripts/generate_template.py

### Test Scripts (already in tests/scripts/)
- docker_test_contract.sh
- generate_test_records.py
- run_contract_test.sh
- run_tests.sh
- run_test_wsl.sh
- test_mcl_in_container.sh

## 🎯 Benefits

1. **🔍 Discoverability**: Easy to find scripts by purpose
2. **🔧 Maintainability**: Clear separation of concerns
3. **📚 Documentation**: Each category has clear purpose
4. **🚀 Onboarding**: New developers can quickly understand script purposes
5. **🔄 CI/CD**: Easier to reference scripts in automation
6. **📦 Modularity**: Scripts can be grouped and executed by category

## 🚀 Next Steps

1. Execute the migration plan
2. Update all references to moved scripts
3. Create category-specific README files
4. Update CI/CD pipelines to use new paths
5. Add script execution permissions where needed
6. Test all scripts in their new locations
