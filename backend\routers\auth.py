"""
Authentication endpoints
"""
from fastapi import APIRouter, Request, Depends
from backend.models.auth import Auth<PERSON><PERSON>enge, AuthVerification, LogoutRequest
from backend.config import success_response, error_response, ADDRESS_ROLE_MAPPING, ROLES
from backend.auth_utils import (
    generate_auth_challenge,
    verify_auth_signature,
    is_authenticated,
    has_role,
    get_role,
    assign_role,
    logout
)

router = APIRouter()

@router.post("/challenge")
async def get_auth_challenge(request: Request):
    """
    Generate an authentication challenge for a wallet address.
    """
    try:
        # Parse the request body
        body = await request.json()
        wallet_address = body.get("wallet_address")

        if not wallet_address:
            return error_response("wallet_address is required", 400)

        challenge = generate_auth_challenge(wallet_address)
        return success_response(
            data={
                "challenge": challenge,
                "wallet_address": wallet_address
            }
        )
    except Exception as e:
        return error_response(str(e), 500)

@router.post("/verify")
async def verify_auth(request: Request):
    """
    Verify an authentication signature.
    """
    try:
        # Parse the request body
        body = await request.json()
        wallet_address = body.get("wallet_address")
        signature = body.get("signature")

        if not wallet_address or not signature:
            error_response("wallet_address and signature are required", 400)

        # Assign role based on wallet address
        role = ADDRESS_ROLE_MAPPING.get(wallet_address.lower())
        if not role:
            # Default to patient role for new addresses
            assign_role(wallet_address, ROLES["PATIENT"])
            role = ROLES["PATIENT"]
        else:
            assign_role(wallet_address, role)

        # Verify the signature
        is_valid = verify_auth_signature(wallet_address, signature)

        if is_valid:
            return success_response(
                data={
                    "authenticated": True,
                    "wallet_address": wallet_address,
                    "role": role
                }
            )
        else:
            return success_response(
                data={
                    "authenticated": False,
                    "wallet_address": wallet_address
                },
                message="Invalid signature"
            )
    except Exception as e:
        error_response(str(e), 500)

@router.post("/logout")
async def logout_user(request: Request):
    """
    Log out a wallet address.
    """
    try:
        # Parse the request body
        body = await request.json()
        wallet_address = body.get("wallet_address")

        if not wallet_address:
            error_response("wallet_address is required", 400)

        success = logout(wallet_address)

        if success:
            return success_response(message="Logged out successfully")
        else:
            return success_response(message="Not logged in")
    except Exception as e:
        error_response(str(e), 500)

@router.get("/status")
async def auth_status(wallet_address: str):
    """
    Check the authentication status of a wallet address.
    """
    try:
        authenticated = is_authenticated(wallet_address)
        role = get_role(wallet_address)

        return success_response(
            data={
                "authenticated": authenticated,
                "wallet_address": wallet_address,
                "role": role
            }
        )
    except Exception as e:
        return error_response(str(e), 500)

@router.post("/assign-role")
async def assign_user_role(
    wallet_address: str,
    role: str,
    admin_wallet_address: str
):
    """
    Assign a role to a wallet address.
    """
    try:
        # Check if the admin is authenticated and has the hospital role
        if not has_role(admin_wallet_address, ROLES["HOSPITAL"]):
            return error_response("Only hospital administrators can assign roles", 403)

        # Assign the role
        success = assign_role(wallet_address, role)

        if success:
            return success_response(
                message=f"Role {role} assigned to {wallet_address}"
            )
        else:
            return error_response("Failed to assign role", 400)
    except Exception as e:
        return error_response(str(e), 500)